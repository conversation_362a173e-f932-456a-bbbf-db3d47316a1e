import {
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import {
  AiPromptVariableUpdateDto,
  CreateAiPromptVariableDto,
} from '../dto/create-ai-prompt-variable.dto';
import { InjectRepository } from '@nestjs/typeorm';
import {
  AiPromptVariable,
  PromptVariableResolver,
} from 'src/models/entities/ai-prompt-variable.entity';
import { Repository } from 'typeorm';
import { InjectPinoLogger, PinoLogger } from 'nestjs-pino';
import { PaginationDto } from 'src/models/dto/pagination.dto';
import { FilterPromptVariableDto } from 'src/models/dto/filters.dto';
import { PromptVariablesResponse } from '../ai-prompt-variable.controller';
import { MetadataDto } from 'src/models/dto/meta-data.dto';
import { ResolverFunctionsResponse } from '../dto/resolver-function-response.dto';

@Injectable()
export class AiPromptVariableService {
  constructor(
    @InjectRepository(AiPromptVariable)
    private aiPromptVariableRepository: Repository<AiPromptVariable>,
    @InjectPinoLogger(AiPromptVariableService.name)
    private readonly logger: PinoLogger,
  ) {}
  async create(
    createAiPromptVariableDto: CreateAiPromptVariableDto,
  ): Promise<AiPromptVariable> {
    const key = createAiPromptVariableDto.key;
    const existingVariable = await this.aiPromptVariableRepository.findOne({
      where: { key: key },
    });

    if (existingVariable) {
      throw new ConflictException(
        'A prompt variable with this key already exists.',
      );
    }

    try {
      const isActive = true;
      const promptVariable = this.aiPromptVariableRepository.create({
        ...createAiPromptVariableDto,
        isActive,
      });
      return await this.aiPromptVariableRepository.save(promptVariable);
    } catch (error) {
      throw error;
    }
  }

  async findAll(
    paginationDto: PaginationDto<FilterPromptVariableDto>,
  ): Promise<PromptVariablesResponse> {
    try {
      const { take, skip, orderBy, orderDirection, filter } = paginationDto;

      const queryBuilder =
        this.aiPromptVariableRepository.createQueryBuilder(
          'ai_prompt_variable',
        );

      if (filter?.type) {
        queryBuilder.andWhere('ai_prompt_variable.type = :type', {
          type: filter.type,
        });
      }

      if (filter?.isActive !== undefined) {
        queryBuilder.andWhere('ai_prompt_variable.isActive = :isActive', {
          isActive: filter.isActive,
        });
      }

      if (filter?.computed !== undefined) {
        queryBuilder.andWhere('ai_prompt_variable.computed = :computed', {
          computed: filter.computed,
        });
      }

      if (filter?.published !== undefined) {
        queryBuilder.andWhere('ai_prompt_variable.published = :published', {
          published: filter.published,
        });
      }

      if (filter?.key) {
        queryBuilder.andWhere('ai_prompt_variable.key ILIKE :key', {
          key: `%${filter.key}%`,
        });
      }

      if (filter?.label) {
        queryBuilder.andWhere('ai_prompt_variable.label ILIKE :label', {
          label: `%${filter.label}%`,
        });
      }

      queryBuilder.addOrderBy(
        `ai_prompt_variable.${orderBy || 'key'}`,
        (orderDirection || 'ASC') as 'ASC' | 'DESC',
      );
      queryBuilder.skip(skip).take(take);

      const [promptVariables, total] = await queryBuilder.getManyAndCount();

      const metadata: MetadataDto = {
        totalCount: total,
        itemCount: promptVariables.length,
        itemsPerPage: take,
        totalPages: Math.ceil(total / take),
        currentPage: skip / take + 1,
      };

      return { results: promptVariables, metadata };
    } catch (error) {
      throw error;
    }
  }

  async findOne(id: string): Promise<AiPromptVariable> {
    try {
      const aiPromptVariable = await this.aiPromptVariableRepository.findOne({
        where: { id },
      });

      if (!aiPromptVariable) {
        throw new NotFoundException(
          `AI Prompt Variable with ID ${id} not found`,
        );
      }

      return aiPromptVariable;
    } catch (error) {
      throw error;
    }
  }

  async update(
    id: string,
    updatePromptVariableDto: AiPromptVariableUpdateDto,
  ): Promise<AiPromptVariable> {
    try {
      const aiPromptVariable = await this.findOne(id);
      Object.assign(aiPromptVariable, updatePromptVariableDto);
      return await this.aiPromptVariableRepository.save(aiPromptVariable);
    } catch (error) {
      throw error;
    }
  }

  async softDelete(id: string) {
    try {
      const aiPromptVariable = await this.findOne(id);
      return await this.aiPromptVariableRepository.softRemove(aiPromptVariable);
    } catch (error) {
      throw error;
    }
  }

  async getResolverFunctions(): Promise<ResolverFunctionsResponse> {
    try {
      const resolverKeys = Object.values(PromptVariableResolver);

      this.logger.info('Fetched available resolver functions.');
      return { results: resolverKeys };
    } catch (error) {
      this.logger.error({ err: error }, 'Error fetching resolver functions.');
      throw error;
    }
  }
}
