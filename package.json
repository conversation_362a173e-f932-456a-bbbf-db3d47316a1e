{"name": "central-config", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "module": "commonjs", "engines": {"node": ">=24.0.0"}, "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/src/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "typeorm": "tsx ./node_modules/typeorm/cli", "migration:run": "npm run typeorm migration:run -- -d ./ormconfig.ts", "migration:generate": "npm run typeorm -- -d ./ormconfig.ts migration:generate ./src/migrations/$npm_config_name", "migration:create": "npm run typeorm -- migration:create ./src/migrations/$npm_config_name", "migration:revert": "npm run typeorm -- -d ./ormconfig.ts migration:revert", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "syncData": "npx ts-node ./src/tools/sync-data.tools.ts", "prepare": "husky"}, "dependencies": {"@clerk/backend": "^2.6.1", "@nestjs/common": "^11.0.0", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.0", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.0", "@nestjs/swagger": "^11.2.0", "@nestjs/typeorm": "^11.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "crypto": "^1.0.1", "dotenv": "^17.2.1", "jwks-rsa": "^3.2.0", "nestjs-pino": "^4.2.0", "papaparse": "^5.5.3", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "pg": "^8.16.3", "qs": "^6.14.0", "reflect-metadata": "^0.1.13", "rimraf": "^3.0.2", "rxjs": "^7.8.1", "typeorm": "^0.3.25"}, "devDependencies": {"@nestjs/cli": "^11.0.8", "@nestjs/schematics": "^9.0.0", "@nestjs/testing": "^11.0.0", "@types/express": "^4.17.21", "@types/jest": "27.0.2", "@types/node": "^18.0.0", "@types/passport-jwt": "^4.0.1", "@types/supertest": "^2.0.11", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "eslint": "^8.0.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "husky": "^9.1.7", "jest": "^27.2.5", "lint-staged": "^16.1.2", "prettier": "^2.3.2", "source-map-support": "^0.5.20", "supertest": "^6.1.3", "ts-jest": "^27.0.3", "ts-loader": "^9.2.3", "ts-node": "^10.0.0", "tsconfig-paths": "^3.10.1", "tsx": "^4.20.3", "typescript": "^4.9.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "husky": {"hooks": {"pre-commit": "npm run build && lint-staged"}}}