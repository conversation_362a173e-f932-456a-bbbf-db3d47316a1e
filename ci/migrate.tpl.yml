apiVersion: batch/v1
kind: Job
metadata:
  name: ${APPLICATION_NAME}
spec:
  template:
    spec:
      containers:
      - name: ${APPLICATION_NAME}
        image: ${IMAGE_REPOSITORY}:${VERSION}
        env:
        - name: DATABASE_HOST
          value: ${DATABASE_HOST}
        - name: DATABASE_PORT
          value: "5432"
        - name: DATABASE_USERNAME
          value: ${DATABASE_USERNAME}
        - name: DATABASE_PASSWORD
          value: ${DATABASE_PASSWORD}
        - name: DATABASE_NAME
          value: clincove-config
      restartPolicy: Never
  backoffLimit: 4