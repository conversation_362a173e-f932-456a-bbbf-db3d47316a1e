import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddVersionStatus1754013009969 implements MigrationInterface {
  name = 'AddVersionStatus1754013009969';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."artifact_category_versions_status_enum" AS ENUM('published', 'draft')`,
    );
    await queryRunner.query(
      `ALTER TABLE "artifact_category_versions" ADD "status" "public"."artifact_category_versions_status_enum" NOT NULL DEFAULT 'published'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "artifact_category_versions" DROP COLUMN "status"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."artifact_category_versions_status_enum"`,
    );
  }
}
