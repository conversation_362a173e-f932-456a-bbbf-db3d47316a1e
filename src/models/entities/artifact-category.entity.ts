import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm';
import {
  ArtifactCategoryVersions,
  InvestigatorInitiatedStudyArtifacts,
  OriginType,
  TMFCore,
} from './artifact-category-version.entity';
import { ArtifactIsfRefModel } from './artifact-isf-ref-model.entity';
import { ArtifactTmfRefModel } from './artifact-tmf-ref-model.entity';
import { BaseEntity } from '../abstracts/abstract-entity';

@Entity({ name: 'artifact_categories', synchronize: false })
export class ArtifactCategories extends BaseEntity {
  @Column({ type: 'varchar', nullable: true })
  tmfZoneNumber: string;

  @Column({ type: 'varchar', nullable: true })
  tmfZoneName: string;

  @Column({ type: 'varchar', nullable: true })
  tmfSectionNumber: string;

  @Column({ type: 'varchar', nullable: true })
  tmfSectionName: string;

  @Column({ type: 'varchar', nullable: true })
  tmfRecordGroupNumber: string;

  @Column({ type: 'varchar', nullable: true })
  tmfRecordGroupName: string;

  @Column({ type: 'varchar', nullable: false })
  recordType: string;

  @Column({ type: 'varchar', nullable: true })
  isfZoneNumber: string;

  @Column({ type: 'varchar', nullable: true })
  isfZoneName: string;

  @Column({ type: 'varchar', nullable: true })
  isfSectionNumber: string;

  @Column({ type: 'varchar', nullable: true })
  isfSectionName: string;

  @Column({ type: 'varchar', nullable: true })
  isfRecordGroupNumber: string;

  @Column({ type: 'varchar', nullable: true })
  isfRecordGroupName: string;

  // --- New Metadata Fields ---
  @Column({ type: 'varchar', nullable: true })
  alternativeNames: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'boolean', nullable: true })
  requiresSignature: boolean;

  @Column({ type: 'boolean', nullable: true })
  expires: boolean;

  @Column({ type: 'boolean', nullable: true })
  inspectableRecord: boolean;

  @Column({ type: 'boolean', nullable: true })
  includesPHI: boolean;

  @Column({ type: 'uuid', nullable: false })
  tmfRefModelId: string;

  @ManyToOne(() => ArtifactTmfRefModel, {
    nullable: true,
    eager: false,
    onUpdate: 'CASCADE',
    onDelete: 'RESTRICT',
  })
  @JoinColumn({ name: 'tmfRefModelId' })
  tmfRefModel: ArtifactTmfRefModel | null;

  @Column({ type: 'uuid', nullable: false })
  isfRefModelId: string;

  @ManyToOne(() => ArtifactIsfRefModel, {
    nullable: true,
    eager: false,
    onUpdate: 'CASCADE',
    onDelete: 'RESTRICT',
  })
  @JoinColumn({ name: 'isfRefModelId' })
  isfRefModel: ArtifactIsfRefModel | null;

  @Column({
    type: 'enum',
    enum: TMFCore,
    nullable: true,
    enumName: 'artifact_categories_tmfcore_enum',
  })
  tmfCore: TMFCore;

  @Column({ type: 'boolean' })
  isTMF: boolean;

  @Column({ type: 'boolean' })
  isISF: boolean;

  @Column({
    type: 'enum',
    enum: InvestigatorInitiatedStudyArtifacts,
    nullable: true,
    name: 'iitStudyArtifacts',
    enumName: 'artifact_categories_iitstudyartifacts_enum',
  })
  iitStudyArtifacts: InvestigatorInitiatedStudyArtifacts;

  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  @Column({
    type: 'enum',
    enum: OriginType,
    nullable: true,
    default: null,
    enumName: 'artifact_categories_origin_enum',
  })
  origin: OriginType | null;

  @ManyToOne(() => ArtifactCategoryVersions, (version) => version.categories)
  @JoinColumn({ name: 'categoryVersionId' })
  categoryVersion: ArtifactCategoryVersions;

  @Column({ type: 'uuid', nullable: true })
  categoryVersionId: string;
}
