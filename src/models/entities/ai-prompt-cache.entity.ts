import { Entity, Column, Index } from 'typeorm';
import { BaseEntity } from '../abstracts/abstract-entity';
import { AiCacheStatus } from '../enums/ai-cache-status.enum';

@Entity({ synchronize: false })
@Index(['lastUpdatedDate'])
@Index(['deletedDate'])
export class AiPromptCache extends BaseEntity {
  @Column({ type: 'varchar', length: 255, nullable: false })
  @Index()
  name: string; // name of the cache entry

  @Column({ type: 'varchar', length: 255, nullable: false })
  @Index()
  cacheContentId: string; // this is the cacheId for the content from gemini

  @Column({ type: 'timestamptz', nullable: false })
  cachedAt: Date; // when the content was cached

  @Column({ type: 'boolean', default: false })
  isPermanent: boolean; // whether or not to remove it from the cache periodically

  @Column({ type: 'timestamptz', nullable: true })
  expiresAt: Date; // when the cache should expire (optional)

  @Column({ type: 'timestamptz', nullable: true })
  lastAccessed: Date; // when the cache was last used (optional)

  @Column({
    type: 'enum',
    enum: AiCacheStatus,
    default: AiCacheStatus.ACTIVE,
  })
  status: AiCacheStatus; // status of the cache (active, expired, invalidated)

  @Column({ type: 'jsonb', nullable: true })
  metadata: any; // additional metadata about the cached content
}
