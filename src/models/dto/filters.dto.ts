/* eslint-disable prettier/prettier */
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';
import {
  AiModelProvider,
  AiPromptTemplateKey,
} from '../entities/ai-prompt-template.entity';
import { PromptVariableType } from '../entities/ai-prompt-variable.entity';
import {
  InvestigatorInitiatedStudyArtifacts,
  OriginType,
} from '../entities/artifact-category-version.entity';

export class BaseFilterDto {}

export class CommonFilterDto extends BaseFilterDto {
  @ApiProperty({
    description: 'Filter by name',
    example: 'Clinical Trial A',
    required: false,
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({
    description: 'Filter by active status',
    example: 'true',
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

export class AiPromptTemplateQueryDto extends CommonFilterDto {
  @ApiPropertyOptional({
    description: 'Filter by prompt template key',
    enum: AiPromptTemplateKey,
    example: AiPromptTemplateKey.PROTOCOL_EXPLORER,
  })
  @IsOptional()
  @IsEnum(AiPromptTemplateKey)
  key?: AiPromptTemplateKey;

  @ApiPropertyOptional({
    description: 'Filter by prompt template version',
    example: 1,
  })
  @IsOptional()
  @IsNumber()
  version?: number;

  @ApiPropertyOptional({
    description: 'Filter by model provider',
    enum: AiModelProvider,
    example: AiModelProvider.GEMINI,
  })
  @IsOptional()
  @IsEnum(AiModelProvider)
  modelProvider?: AiModelProvider;

  @ApiPropertyOptional({
    description:
      'Filter by specific model name (case-insensitive partial match)',
    example: 'gemini-1.5-pro',
  })
  @IsOptional()
  @IsString()
  model?: string;
}
export class FilterPromptVariableDto extends BaseFilterDto {
  @IsOptional()
  @IsString()
  key?: string;

  @IsOptional()
  @IsString()
  label?: string;

  @IsOptional()
  @IsEnum(PromptVariableType)
  type?: PromptVariableType;

  @IsOptional()
  @IsBoolean()
  computed?: boolean;

  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @IsOptional()
  @IsBoolean()
  published?: boolean;
}

export class FilterTmfRefModelDto extends BaseFilterDto {
  @IsOptional()
  @IsString()
  tmfRefModel?: string;
}

export class FilterIsfRefModelDto extends BaseFilterDto {
  @IsOptional()
  @IsString()
  isfRefModel?: string;
}

export class FilterArtifactCategoriesDto extends BaseFilterDto {
  @IsOptional()
  @IsString()
  tmfZoneName?: string;

  @IsOptional()
  @IsString()
  tmfSectionName?: string;

  @IsOptional()
  @IsString()
  tmfRecordGroupName?: string;

  @IsOptional()
  @IsString()
  isfZoneName?: string;

  @IsOptional()
  @IsString()
  isfSectionName?: string;

  @IsOptional()
  @IsString()
  isfRecordGroupName?: string;

  @IsOptional()
  @IsString()
  recordType?: string;

  @IsOptional()
  @IsString()
  alternativeNames?: string;

  @IsOptional()
  @IsUUID()
  categoryVersionId?: string;

  @IsOptional()
  @IsUUID()
  studyId?: string;

  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @IsOptional()
  @IsBoolean()
  isTMF?: boolean;

  @IsOptional()
  @IsBoolean()
  isISF?: boolean;

  @IsOptional()
  @IsString()
  versionLatest?: string;

  @IsOptional()
  @IsString()
  version?: string;

  @IsOptional()
  @IsEnum(OriginType)
  origin?: OriginType;

  @IsOptional()
  @IsEnum(InvestigatorInitiatedStudyArtifacts)
  iitStudyArtifacts?: InvestigatorInitiatedStudyArtifacts;

  @ApiProperty()
  @IsOptional()
  @IsArray()
  orders?: OrderItem[];

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  requiresSignature?: boolean;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  expires?: boolean;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  inspectableRecord?: boolean;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  includesPHI?: boolean;
}

export class OrderItem {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  orderBy: string;

  @ApiProperty({
    enum: ['ASC', 'DESC'],
  })
  @IsString()
  @IsNotEmpty()
  orderDirection: 'DESC' | 'ASC';
}

export class FilterArtifactCategoryVersionDto extends BaseFilterDto {
  @IsOptional()
  @IsNumber()
  version?: number;
}
