import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, <PERSON><PERSON>han, Find<PERSON>anyOptions, IsNull, Not } from 'typeorm';
import {
  ArtifactCategoryVersions,
  VersionStatus,
} from '../../models/entities/artifact-category-version.entity';
import { AiPromptTemplate } from '../../models/entities/ai-prompt-template.entity';
import { AiPromptCache } from '../../models/entities/ai-prompt-cache.entity';
import { AiPromptVariable } from '../../models/entities/ai-prompt-variable.entity';
import { ArtifactIsfRefModel } from '../../models/entities/artifact-isf-ref-model.entity';
import { ArtifactTmfRefModel } from '../../models/entities/artifact-tmf-ref-model.entity';
import {
  DeletionsDataResponse,
  FetchChangeByEntityRequestDto,
  SyncDataByEntityResponse,
  SyncUpdatesResponseDto,
  UpdatesDataResponse,
} from '../dtos/sync-updates.dto';
import { ArtifactCategories } from '../../models/entities/artifact-category.entity';
import { UpdateEntityTypeEnum } from '../enums/update-entity-type.enum';
import { ChangeMethodEnum } from '../enums/change-method.enum';

@Injectable()
export class SyncService {
  constructor(
    @InjectRepository(ArtifactCategoryVersions)
    private artifactCategoryVersionsRepo: Repository<ArtifactCategoryVersions>,
    @InjectRepository(ArtifactCategories)
    private artifactCategoriesRepo: Repository<ArtifactCategories>,
    @InjectRepository(AiPromptTemplate)
    private aiPromptTemplateRepo: Repository<AiPromptTemplate>,
    @InjectRepository(AiPromptCache)
    private aiPromptCacheRepo: Repository<AiPromptCache>,
    @InjectRepository(AiPromptVariable)
    private aiPromptVariableRepo: Repository<AiPromptVariable>,
    @InjectRepository(ArtifactIsfRefModel)
    private artifactIsfRefModelRepo: Repository<ArtifactIsfRefModel>,
    @InjectRepository(ArtifactTmfRefModel)
    private artifactTmfRefModelRepo: Repository<ArtifactTmfRefModel>,
  ) {}
  async fetchByEntity(
    request: FetchChangeByEntityRequestDto,
  ): Promise<SyncDataByEntityResponse> {
    const { page, take, entityType, method, lastSyncTimestamp } = request;

    if (!lastSyncTimestamp) {
      throw new BadRequestException('lastSyncTimestamp was not be provided');
    }

    const lastSyncDate = new Date(lastSyncTimestamp);
    const updatedConditions: FindManyOptions<any> = {
      where: { lastUpdatedDate: MoreThan(lastSyncDate) },
      take,
      skip: (page - 1) * take,
    };
    const deletedConditions: FindManyOptions<any> = {
      where: { deletedDate: MoreThan(lastSyncDate) },
      withDeleted: true,
      take,
      skip: (page - 1) * take,
    };
    let data = [];
    let count = 0;

    switch (entityType) {
      case UpdateEntityTypeEnum.aiPromptCache:
        if (method === ChangeMethodEnum.update) {
          [data, count] = await this.aiPromptCacheRepo.findAndCount(
            updatedConditions,
          );
        } else {
          [data, count] = await this.aiPromptCacheRepo.findAndCount(
            deletedConditions,
          );
        }
        break;
      case UpdateEntityTypeEnum.aiPromptTemplate:
        if (method === ChangeMethodEnum.update) {
          [data, count] = await this.aiPromptTemplateRepo.findAndCount(
            updatedConditions,
          );
        } else {
          [data, count] = await this.aiPromptTemplateRepo.findAndCount(
            deletedConditions,
          );
        }
        break;
      case UpdateEntityTypeEnum.aiPromptVariable:
        if (method === ChangeMethodEnum.update) {
          [data, count] = await this.aiPromptVariableRepo.findAndCount(
            updatedConditions,
          );
        } else {
          [data, count] = await this.aiPromptVariableRepo.findAndCount(
            deletedConditions,
          );
        }
        break;
      case UpdateEntityTypeEnum.artifactCategories:
        if (method === ChangeMethodEnum.update) {
          [data, count] = await this.artifactCategoriesRepo.findAndCount({
            where: [
              {
                lastUpdatedDate: MoreThan(lastSyncDate),
                categoryVersionId: IsNull(),
              },
              {
                lastUpdatedDate: MoreThan(lastSyncDate),
                categoryVersionId: Not(IsNull()),
                categoryVersion: {
                  status: VersionStatus.PUBLISHED,
                },
              },
            ],
            relations: ['categoryVersion'],
            take,
            skip: (page - 1) * take,
          });
        } else {
          [data, count] = await this.artifactCategoriesRepo.findAndCount(
            deletedConditions,
          );
        }
        break;
      case UpdateEntityTypeEnum.artifactCategoryVersions:
        if (method === ChangeMethodEnum.update) {
          [data, count] = await this.artifactCategoryVersionsRepo.findAndCount({
            where: {
              lastUpdatedDate: MoreThan(lastSyncDate),
              status: VersionStatus.PUBLISHED,
            },
            take,
            skip: (page - 1) * take,
          });
        } else {
          [data, count] = await this.artifactCategoryVersionsRepo.findAndCount(
            deletedConditions,
          );
        }
        break;
      case UpdateEntityTypeEnum.artifactIsfRefModel:
        if (method === ChangeMethodEnum.update) {
          [data, count] = await this.artifactIsfRefModelRepo.findAndCount(
            updatedConditions,
          );
        } else {
          [data, count] = await this.artifactIsfRefModelRepo.findAndCount(
            deletedConditions,
          );
        }
        break;
      case UpdateEntityTypeEnum.artifactTmfRefModel:
        if (method === ChangeMethodEnum.update) {
          [data, count] = await this.artifactTmfRefModelRepo.findAndCount(
            updatedConditions,
          );
        } else {
          [data, count] = await this.artifactTmfRefModelRepo.findAndCount(
            deletedConditions,
          );
        }
        break;
      default:
        throw new BadRequestException('Invalid entity type');
    }

    return {
      data,
      count,
    };
  }
  async getSyncUpdates(
    lastSyncTimestamp: string,
    page: number,
    take: number,
  ): Promise<SyncUpdatesResponseDto> {
    const syncTimestamp = new Date().toISOString();
    const updates = new UpdatesDataResponse();
    const deletions = new DeletionsDataResponse();

    if (!lastSyncTimestamp) {
      throw new BadRequestException('lastSyncTimestamp was not be provided');
    }
    const lastSyncDate = new Date(lastSyncTimestamp);

    const updatedConditions: FindManyOptions<any> = {
      where: { lastUpdatedDate: MoreThan(lastSyncDate) },
      take,
      skip: (page - 1) * take,
    };

    // Query for updates in each entity
    const [
      artifactCategoryVersions,
      updatedAiPromptTemplates,
      updatedAiPromptCaches,
      updatedAiPromptVariables,
      updatedArtifactIsfRefModels,
      updatedArtifactTmfRefModels,
    ] = await Promise.all([
      this.artifactCategoryVersionsRepo.find({
        where: {
          lastUpdatedDate: MoreThan(lastSyncDate),
          status: VersionStatus.PUBLISHED,
        },
        take,
        skip: (page - 1) * take,
      }),
      this.aiPromptTemplateRepo.find(updatedConditions),
      this.aiPromptCacheRepo.find(updatedConditions),
      this.aiPromptVariableRepo.find(updatedConditions),
      this.artifactIsfRefModelRepo.find(updatedConditions),
      this.artifactTmfRefModelRepo.find(updatedConditions),
    ]);

    const updatedArtifactCategories = await this.artifactCategoriesRepo.find({
      where: [
        {
          lastUpdatedDate: MoreThan(lastSyncDate),
          categoryVersionId: IsNull(),
        },
        {
          lastUpdatedDate: MoreThan(lastSyncDate),
          categoryVersionId: Not(IsNull()),
          categoryVersion: {
            status: VersionStatus.PUBLISHED,
          },
        },
      ],
      relations: ['categoryVersion'],
      take,
      skip: (page - 1) * take,
    });

    updates.artifactCategories = updatedArtifactCategories;
    updates.artifactCategoryVersions = artifactCategoryVersions;
    updates.aiPromptTemplate = updatedAiPromptTemplates;
    updates.aiPromptCache = updatedAiPromptCaches;
    updates.aiPromptVariable = updatedAiPromptVariables;
    updates.artifactIsfRefModel = updatedArtifactIsfRefModels;
    updates.artifactTmfRefModel = updatedArtifactTmfRefModels;

    const deletedConditions: FindManyOptions<any> = {
      where: { deletedDate: MoreThan(lastSyncDate) },
      withDeleted: true,
      take,
      skip: (page - 1) * take,
    };
    const [
      deletedArtifactCategories,
      deletedArtifactCategoryVersions,
      deletedAiPromptTemplates,
      deletedAiPromptCaches,
      deletedAiPromptVariables,
      deletedArtifactIsfRefModels,
      deletedArtifactTmfRefModels,
    ] = await Promise.all([
      this.artifactCategoriesRepo.find(deletedConditions),
      this.artifactCategoryVersionsRepo.find(deletedConditions),
      this.aiPromptTemplateRepo.find(deletedConditions),
      this.aiPromptCacheRepo.find(deletedConditions),
      this.aiPromptVariableRepo.find(deletedConditions),
      this.artifactIsfRefModelRepo.find(deletedConditions),
      this.artifactTmfRefModelRepo.find(deletedConditions),
    ]);

    deletions.artifactCategories = deletedArtifactCategories.map((x) => x.id);
    deletions.artifactCategoryVersions = deletedArtifactCategoryVersions.map(
      (x) => x.id,
    );
    deletions.aiPromptTemplate = deletedAiPromptTemplates.map((x) => x.id);
    deletions.aiPromptCache = deletedAiPromptCaches.map((x) => x.id);
    deletions.aiPromptVariable = deletedAiPromptVariables.map((x) => x.id);
    deletions.artifactIsfRefModel = deletedArtifactIsfRefModels.map(
      (x) => x.id,
    );
    deletions.artifactTmfRefModel = deletedArtifactTmfRefModels.map(
      (x) => x.id,
    );

    return {
      syncTimestamp,
      updates,
      deletions,
    };
  }

  async getArtifactCategoryVersions(): Promise<ArtifactCategoryVersions[]> {
    return this.artifactCategoryVersionsRepo.find({
      order: { version: 'DESC' },
    });
  }

  async getAiPromptTemplates(): Promise<AiPromptTemplate[]> {
    return this.aiPromptTemplateRepo.find({
      where: { isActive: true },
      order: { name: 'ASC' },
    });
  }

  async getAiPromptCaches(): Promise<AiPromptCache[]> {
    return this.aiPromptCacheRepo.find({
      order: { name: 'ASC' },
    });
  }

  async getAiPromptVariables(): Promise<AiPromptVariable[]> {
    return this.aiPromptVariableRepo.find({
      where: { isActive: true },
      order: { key: 'ASC' },
    });
  }

  async getArtifactIsfRefModels(): Promise<ArtifactIsfRefModel[]> {
    return this.artifactIsfRefModelRepo.find({
      order: { isfRefModel: 'ASC' },
    });
  }

  async getArtifactTmfRefModels(): Promise<ArtifactTmfRefModel[]> {
    return this.artifactTmfRefModelRepo.find({
      order: { tmfRefModel: 'ASC' },
    });
  }
}
