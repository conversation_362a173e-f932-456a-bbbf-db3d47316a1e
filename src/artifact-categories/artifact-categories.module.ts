import { Module } from '@nestjs/common';
import { ArtifactCategoriesService } from './artifact-categories.service';
import { ArtifactCategoriesController } from './artifact-categories.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ArtifactCategories } from 'src/models/entities/artifact-category.entity';
import { ArtifactCategoryVersions } from 'src/models/entities/artifact-category-version.entity';
import { ArtifactTmfRefModel } from 'src/models/entities/artifact-tmf-ref-model.entity';
import { ArtifactIsfRefModel } from 'src/models/entities/artifact-isf-ref-model.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      ArtifactCategories,
      ArtifactCategoryVersions,
      ArtifactTmfRefModel,
      ArtifactIsfRefModel,
    ]),
  ],
  controllers: [ArtifactCategoriesController],
  providers: [ArtifactCategoriesService],
})
export class ArtifactCategoriesModule {}
