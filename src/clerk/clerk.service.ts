/* eslint-disable prettier/prettier */
import { Injectable } from '@nestjs/common';
import { ClerkClient, createClerkClient } from '@clerk/backend'

@Injectable()
export class ClerkService {
    private readonly clerkClient: ClerkClient;

    // constructor(@InjectPinoLogger(ClerkService.name) protected readonly logger: <PERSON><PERSON><PERSON>og<PERSON>,) {
    constructor() {
        this.clerkClient = createClerkClient({ secretKey: process.env.CLERK_SECRET_KEY })
    }

    public async getMetadata(userId: string): Promise<any> {
        try {
            const user = await this.clerkClient.users.getUser(userId);
            return user.publicMetadata;
        } catch (error) {
            console.error('Error getting user with metadata:', error);
            throw error;
        }
    }
}
