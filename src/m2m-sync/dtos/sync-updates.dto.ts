import { <PERSON><PERSON><PERSON>, IsISO8601, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { ArtifactCategoryVersions } from '../../models/entities/artifact-category-version.entity';
import { AiPromptTemplate } from '../../models/entities/ai-prompt-template.entity';
import { AiPromptCache } from '../../models/entities/ai-prompt-cache.entity';
import { AiPromptVariable } from '../../models/entities/ai-prompt-variable.entity';
import { ArtifactIsfRefModel } from '../../models/entities/artifact-isf-ref-model.entity';
import { ArtifactTmfRefModel } from '../../models/entities/artifact-tmf-ref-model.entity';
import { ArtifactCategories } from '../../models/entities/artifact-category.entity';
import { Type } from 'class-transformer';
import { UpdateEntityTypeEnum } from '../enums/update-entity-type.enum';
import { ChangeMethodEnum } from '../enums/change-method.enum';

export class SyncUpdatesQueryDto {
  @ApiProperty({
    description: 'ISO 8601 timestamp of the last sync',
    example: '2024-01-15T10:30:00.000Z',
    required: false,
  })
  @IsNotEmpty()
  @IsISO8601()
  lastSyncTimestamp: string;

  @ApiProperty()
  @IsNotEmpty()
  @Type(() => Number)
  @IsNumber()
  page: number;

  @ApiProperty()
  @IsNotEmpty()
  @Type(() => Number)
  @IsNumber()
  take: number;
}

export class FetchChangeByEntityRequestDto extends SyncUpdatesQueryDto {
  @ApiProperty({
    description: 'Entity type',
    enum: UpdateEntityTypeEnum,
  })
  @IsEnum(UpdateEntityTypeEnum)
  @IsNotEmpty()
  entityType: UpdateEntityTypeEnum;

  @ApiProperty({
    description: 'Change method type',
    enum: ChangeMethodEnum,
  })
  @IsEnum(ChangeMethodEnum)
  @IsNotEmpty()
  method: ChangeMethodEnum;
}

export class UpdatesDataResponse {
  artifactCategories: ArtifactCategories[];
  artifactCategoryVersions: ArtifactCategoryVersions[];
  aiPromptTemplate: AiPromptTemplate[];
  aiPromptCache: AiPromptCache[];
  aiPromptVariable: AiPromptVariable[];
  artifactIsfRefModel: ArtifactIsfRefModel[];
  artifactTmfRefModel: ArtifactTmfRefModel[];
}

export class DeletionsDataResponse {
  artifactCategories: string[];
  artifactCategoryVersions: string[];
  aiPromptTemplate: string[];
  aiPromptCache: string[];
  aiPromptVariable: string[];
  artifactIsfRefModel: string[];
  artifactTmfRefModel: string[];
}

export class SyncUpdatesResponseDto {
  @ApiProperty({
    description: 'Current sync timestamp',
    example: '2024-01-15T10:30:00.000Z',
  })
  syncTimestamp: string;

  @ApiProperty({
    description: 'Updated records grouped by model',
  })
  updates: UpdatesDataResponse;

  @ApiProperty({
    description: 'Deleted record IDs grouped by model',
  })
  deletions: DeletionsDataResponse;
}

export class SyncDataByEntityResponse {
  count: number;
  data: any[];
}
