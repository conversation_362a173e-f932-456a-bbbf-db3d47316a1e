import { Test, TestingModule } from '@nestjs/testing';
import { AiPromptVariableController } from './ai-prompt-variable.controller';
import { AiPromptVariableService } from './services/ai-prompt-variable.service';

describe('AiPromptVariableController', () => {
  let controller: AiPromptVariableController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AiPromptVariableController],
      providers: [AiPromptVariableService],
    }).compile();

    controller = module.get<AiPromptVariableController>(
      AiPromptVariableController,
    );
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
