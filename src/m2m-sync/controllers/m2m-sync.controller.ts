import {
  Controller,
  Get,
  Query,
  UseGuards,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiSecurity,
} from '@nestjs/swagger';
import { SyncService } from '../services/m2m-sync.service';
import { InternalApiGuard } from '../../guards/internal-api.guard';
import {
  FetchChangeByEntityRequestDto,
  SyncDataByEntityResponse,
  SyncUpdatesQueryDto,
  SyncUpdatesResponseDto,
} from '../dtos/sync-updates.dto';
import { ArtifactCategoryVersions } from '../../models/entities/artifact-category-version.entity';
import { AiPromptTemplate } from '../../models/entities/ai-prompt-template.entity';
import { AiPromptVariable } from '../../models/entities/ai-prompt-variable.entity';
import { ArtifactIsfRefModel } from '../../models/entities/artifact-isf-ref-model.entity';
import { ArtifactTmfRefModel } from '../../models/entities/artifact-tmf-ref-model.entity';
import { AiPromptCache } from '../../models/entities/ai-prompt-cache.entity';

@ApiTags('M2M Sync')
@ApiSecurity('x-api-key')
@Controller({ path: 'sync', version: '1' })
@UseGuards(InternalApiGuard)
export class SyncController {
  constructor(private readonly syncService: SyncService) {}

  @Get('updates')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get configuration updates since last sync',
    description:
      'Retrieves all records that have been updated since the provided timestamp',
  })
  @ApiResponse({
    status: 200,
    description: 'Configuration updates retrieved successfully',
    type: SyncUpdatesResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing API key',
  })
  async getSyncUpdates(
    @Query() query: SyncUpdatesQueryDto,
  ): Promise<SyncUpdatesResponseDto> {
    return await this.syncService.getSyncUpdates(
      query.lastSyncTimestamp,
      query.page,
      query.take,
    );
  }
  @Get('fetch-by-entity')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get configuration updates by entity since last sync',
    description:
      'Retrieves all records that have been updated since the provided timestamp',
  })
  @ApiResponse({
    status: 200,
    description: 'Configuration updates retrieved successfully',
    type: SyncUpdatesResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing API key',
  })
  async fetchByEntity(
    @Query() query: FetchChangeByEntityRequestDto,
  ): Promise<SyncDataByEntityResponse> {
    return await this.syncService.fetchByEntity(query);
  }

  @Get('artifact-category-versions')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get latest artifact category versions',
    description: 'Retrieves all artifact category versions ordered by version',
  })
  @ApiResponse({
    status: 200,
    description: 'Latest artifact category versions retrieved successfully',
    type: [ArtifactCategoryVersions],
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing API key',
  })
  async getLatestArtifactCategoryVersions(): Promise<
    ArtifactCategoryVersions[]
  > {
    return this.syncService.getArtifactCategoryVersions();
  }

  @Get('ai-prompt-templates')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get active AI prompt templates',
    description: 'Retrieves all active AI prompt templates',
  })
  @ApiResponse({
    status: 200,
    description: 'AI prompt templates retrieved successfully',
    type: [AiPromptTemplate],
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing API key',
  })
  async getAiPromptTemplates(): Promise<AiPromptTemplate[]> {
    return this.syncService.getAiPromptTemplates();
  }

  @Get('ai-prompt-caches')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get active AI prompt caches',
    description: 'Retrieves all active AI prompt caches',
  })
  @ApiResponse({
    status: 200,
    description: 'AI prompt caches retrieved successfully',
    type: [AiPromptTemplate],
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing API key',
  })
  async getAiPromptCaches(): Promise<AiPromptCache[]> {
    return this.syncService.getAiPromptCaches();
  }

  @Get('ai-prompt-variables')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get active AI prompt variables',
    description: 'Retrieves all active AI prompt variables',
  })
  @ApiResponse({
    status: 200,
    description: 'AI prompt variables retrieved successfully',
    type: [AiPromptVariable],
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing API key',
  })
  async getAiPromptVariables(): Promise<AiPromptVariable[]> {
    return this.syncService.getAiPromptVariables();
  }

  @Get('artifact-isf-ref-models')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get artifact ISF reference models',
    description: 'Retrieves all artifact ISF reference models',
  })
  @ApiResponse({
    status: 200,
    description: 'Artifact ISF reference models retrieved successfully',
    type: [ArtifactIsfRefModel],
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing API key',
  })
  async getArtifactIsfRefModels(): Promise<ArtifactIsfRefModel[]> {
    return this.syncService.getArtifactIsfRefModels();
  }

  @Get('artifact-tmf-ref-models')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get artifact TMF reference models',
    description: 'Retrieves all artifact TMF reference models',
  })
  @ApiResponse({
    status: 200,
    description: 'Artifact TMF reference models retrieved successfully',
    type: [ArtifactTmfRefModel],
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing API key',
  })
  async getArtifactTmfRefModels(): Promise<ArtifactTmfRefModel[]> {
    return this.syncService.getArtifactTmfRefModels();
  }
}
