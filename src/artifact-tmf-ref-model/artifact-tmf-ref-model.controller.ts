import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
  ParseUUIDPipe,
  Put,
} from '@nestjs/common';
import { ArtifactTmfRefModelService } from './artifact-tmf-ref-model.service';
import {
  TmfRefModelCreateDto,
  TmfRefModelUpdateDto,
} from './dto/create-artifact-tmf-ref-model.dto';
import { ApiResponse, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from 'src/auth/jwt-auth.guard';
import { RolesGuard } from 'src/auth/roles/roles.guard';
import { InjectPinoLogger, PinoLogger } from 'nestjs-pino';
import { ArtifactTmfRefModel } from 'src/models/entities/artifact-tmf-ref-model.entity';
import { Roles } from 'src/auth/roles/roles.decorator';
import { PaginationDto } from 'src/models/dto/pagination.dto';
import { FilterTmfRefModelDto } from 'src/models/dto/filters.dto';
import { MetadataDto } from 'src/models/dto/meta-data.dto';

@ApiTags('Artifact Tmf Ref Model')
@UseGuards(JwtAuthGuard, RolesGuard)
@Controller({
  path: ['admin/artifact-tmf-ref-model'],
  version: '1',
})
export class ArtifactTmfRefModelController {
  constructor(
    private readonly artifactTmfRefModelService: ArtifactTmfRefModelService,
    @InjectPinoLogger(ArtifactTmfRefModelController.name)
    protected readonly logger: PinoLogger,
  ) {}

  @Post()
  @Roles('admin')
  @ApiResponse({
    status: 201,
    description: 'The tmf ref model has been successfully created.',
    type: ArtifactTmfRefModel,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - validation error',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - insufficient permissions',
  })
  async createTmfRefModel(
    @Body() refModelCreateDto: TmfRefModelCreateDto,
  ): Promise<ArtifactTmfRefModel> {
    try {
      return await this.artifactTmfRefModelService.createTmfRefModel(
        refModelCreateDto,
      );
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  @Get()
  @Roles('admin')
  @ApiResponse({
    status: 200,
    description: 'Get a paginated list of tmf ref model.',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - validation error',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - insufficient permissions',
  })
  async getTmfRefModel(
    @Query() paginationDto: PaginationDto<FilterTmfRefModelDto>,
  ): Promise<{ results: ArtifactTmfRefModel[]; metadata: MetadataDto }> {
    try {
      return await this.artifactTmfRefModelService.getTmfRefModel(
        paginationDto,
      );
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  @Get(':modelId')
  @Roles('admin')
  @ApiResponse({
    status: 200,
    description: 'Get a tmf ref model by ID.',
    type: ArtifactTmfRefModel,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - validation error',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - insufficient permissions',
  })
  async getTmfRefModelById(
    @Param('modelId', ParseUUIDPipe) modelId: string,
  ): Promise<ArtifactTmfRefModel> {
    try {
      return await this.artifactTmfRefModelService.getTmfRefModelById(modelId);
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  @Put(':modelId')
  @Roles('admin')
  @ApiResponse({
    status: 200,
    description: 'Update a tmf ref model successfully',
    type: ArtifactTmfRefModel,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - validation error',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - insufficient permissions',
  })
  async updateTmfRefModel(
    @Param('modelId', ParseUUIDPipe) modelId: string,
    @Body() refModelUpdateDto: TmfRefModelUpdateDto,
  ): Promise<ArtifactTmfRefModel> {
    try {
      return await this.artifactTmfRefModelService.updateTmfRefModel(
        modelId,
        refModelUpdateDto,
      );
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  @Delete(':modelId')
  @Roles('admin')
  @ApiResponse({
    status: 200,
    description: 'Delete a ref model',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - validation error',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description:
      'Attempt to delete a ref model that links to an artifact category.',
  })
  async deleteRefModel(@Param('modelId', ParseUUIDPipe) modelId: string) {
    try {
      await this.artifactTmfRefModelService.deleteRefModel(modelId);
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }
}
