replicaCount: ${REPLIC<PERSON>}

image:
  repository: ${IMAGE_REPOSITORY}
  tag: ${VERSION}
  pullPolicy: IfNotPresent

service:
  type: NodePort
  port: 80
  containerPort: 3000

resources:
  limits:
    cpu: ${CPU}m
    memory: ${RAM}Mi
  requests:
    cpu: ${CPU}m
    memory: ${RAM}Mi

livenessProbe:
  httpGet:
    path: /
    port: http
    scheme: HTTP
  initialDelaySeconds: 30
  periodSeconds: 30
  timeoutSeconds: 5

readinessProbe:
  httpGet:
    path: /
    port: http
    scheme: HTTP
  initialDelaySeconds: 30
  periodSeconds: 30
  timeoutSeconds: 5

configmap:
  APP_ENV: ${APP_ENV}
  ACCESS_TOKEN_EXPIRES_IN: "1h"
  DATABASE_LOG: ${DATABASE_LOG}
  DATABASE_PORT: 5432
  DATABASE_NAME: "clincove-config"
  MAIN_APP_DATABASE_PORT: 5432
  MAIN_APP_DATABASE_NAME: "clincove"
  CORS_ORIGINS: ${CORS_ORIGINS}

secretLink:
  DATABASE_HOST: ${DATABASE_HOST}
  DATABASE_USERNAME: ${DATABASE_USERNAME}
  DATABASE_PASSWORD: ${DATABASE_PASSWORD}
  MAIN_APP_DATABASE_HOST: ${DATABASE_HOST}
  MAIN_APP_DATABASE_USERNAME: ${DATABASE_USERNAME}
  MAIN_APP_DATABASE_PASSWORD: ${DATABASE_PASSWORD}
  CLERK_SECRET_KEY: ${CLERK_SECRET_KEY}
  INTERNAL_API_KEY: ${INTERNAL_API_KEY}