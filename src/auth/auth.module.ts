/* eslint-disable prettier/prettier */
import { Modu<PERSON> } from '@nestjs/common';
import { PassportModule } from '@nestjs/passport';
import { JwtModule } from '@nestjs/jwt';
import { JwtStrategy } from './jwt.strategy'; 
import { ConfigService } from '@nestjs/config'; 
import { ClerkService } from 'src/clerk/clerk.service';

@Module({
  imports: [

    PassportModule.register({ defaultStrategy: 'jwt' }),

    JwtModule.registerAsync({
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get<string>('SECRET_KEY'), 
        signOptions: {
          expiresIn: configService.get<string>('ACCESS_TOKEN_EXPIRES_IN'),
        },
      }),
    }),
  ],
  providers: [JwtStrategy, ClerkService],
  exports: [PassportModule],
})
export class AuthModule {}