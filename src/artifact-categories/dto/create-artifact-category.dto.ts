import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  ValidateIf,
  IsNotEmpty,
  IsUUID,
  IsString,
  IsOptional,
  IsBoolean,
  IsEnum,
  IsDate,
  IsNumber,
} from 'class-validator';
import {
  TMF<PERSON>ore,
  InvestigatorInitiatedStudyArtifacts,
  OriginType,
} from 'src/models/entities/artifact-category-version.entity';

export class CreateArtifactCategoryDto {
  @ApiProperty({
    description: 'TMF reference model ID. Required if isTMF is true.',
    required: false,
  })
  @ValidateIf((o) => o.isTMF === true)
  @IsNotEmpty({ message: 'tmfRefModelId is required when isTMF is true' })
  @IsUUID()
  tmfRefModelId?: string;

  @ApiProperty({
    description: 'ISF reference model ID. Required if isISF is true.',
    required: false,
  })
  @ValidateIf((o) => o.isISF === true)
  @IsNotEmpty({ message: 'isfRefModelId is required when isISF is true' })
  @IsUUID()
  isfRefModelId?: string;

  // --- RENAMED: TMF Structure Fields (Conditionally Required) ---

  @ApiProperty({
    description: 'TMF Zone Number. Required if isTMF is true.',
    required: false,
  })
  @ValidateIf((o) => o.isTMF === true)
  @IsNotEmpty({ message: 'tmfZoneNumber is required when isTMF is true' })
  @IsString()
  tmfZoneNumber?: string;

  @ApiProperty({
    description: 'TMF Zone Name. Required if isTMF is true.',
    required: false,
  })
  @ValidateIf((o) => o.isTMF === true)
  @IsNotEmpty({ message: 'tmfZoneName is required when isTMF is true' })
  @IsString()
  tmfZoneName?: string;

  @ApiProperty({
    description: 'TMF Section Number. Required if isTMF is true.',
    required: false,
  })
  @ValidateIf((o) => o.isTMF === true)
  @IsNotEmpty({ message: 'tmfSectionNumber is required when isTMF is true' })
  @IsString()
  tmfSectionNumber?: string;

  @ApiProperty({
    description: 'TMF Section Name. Required if isTMF is true.',
    required: false,
  })
  @ValidateIf((o) => o.isTMF === true)
  @IsNotEmpty({ message: 'tmfSectionName is required when isTMF is true' })
  @IsString()
  tmfSectionName?: string;

  @ApiProperty({
    description:
      'TMF Record Group Number (formerly Artifact Number). Required if isTMF is true.',
    required: false,
  })
  @ValidateIf((o) => o.isTMF === true)
  @IsNotEmpty({
    message: 'tmfRecordGroupNumber is required when isTMF is true',
  })
  @IsString()
  tmfRecordGroupNumber?: string;

  @ApiProperty({
    description:
      'TMF Record Group Name (formerly Artifact Name). Required if isTMF is true.',
    required: false,
  })
  @ValidateIf((o) => o.isTMF === true)
  @IsNotEmpty({ message: 'tmfRecordGroupName is required when isTMF is true' })
  @IsString()
  tmfRecordGroupName?: string;

  @ApiProperty({
    description: 'ISF Zone Number. Required if isISF is true.',
    required: false,
  })
  @ValidateIf((o) => o.isISF === true)
  @IsNotEmpty({ message: 'isfZoneNumber is required when isISF is true' })
  @IsString()
  isfZoneNumber?: string;

  @ApiProperty({
    description: 'ISF Zone Name. Required if isISF is true.',
    required: false,
  })
  @ValidateIf((o) => o.isISF === true)
  @IsNotEmpty({ message: 'isfZoneName is required when isISF is true' })
  @IsString()
  isfZoneName?: string;

  @ApiProperty({
    description: 'ISF Section Number. Required if isISF is true.',
    required: false,
  })
  @ValidateIf((o) => o.isISF === true)
  @IsNotEmpty({ message: 'isfSectionNumber is required when isISF is true' })
  @IsString()
  isfSectionNumber?: string;

  @ApiProperty({
    description: 'ISF Section Name. Required if isISF is true.',
    required: false,
  })
  @ValidateIf((o) => o.isISF === true)
  @IsNotEmpty({ message: 'isfSectionName is required when isISF is true' })
  @IsString()
  isfSectionName?: string;

  @ApiProperty({
    description: 'ISF Record Group Number. Required if isISF is true.',
    required: false,
  })
  @ValidateIf((o) => o.isISF === true)
  @IsNotEmpty({
    message: 'isfRecordGroupNumber is required when isISF is true',
  })
  @IsString()
  isfRecordGroupNumber?: string;

  @ApiProperty({
    description: 'ISF Record Group Name. Required if isISF is true.',
    required: false,
  })
  @ValidateIf((o) => o.isISF === true)
  @IsNotEmpty({ message: 'isfRecordGroupName is required when isISF is true' })
  @IsString()
  isfRecordGroupName?: string;

  // --- RENAMED: subartifact -> recordType (Now always required) ---

  @ApiProperty({
    description:
      'The specific type of record (formerly Subartifact). This field is always required.',
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  recordType: string;

  // --- NEW: Metadata Fields (All optional) ---

  @ApiProperty({
    description: 'Optional alternative names of the artifact category.',
    required: false,
  })
  @IsOptional()
  @IsString()
  alternativeNames?: string;

  @ApiProperty({
    description: 'Optional detailed description of the artifact category.',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Indicates if the record requires a signature.',
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  requiresSignature?: boolean;

  @ApiProperty({
    description: 'Indicates if the record has an expiration date.',
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  expires?: boolean;

  @ApiProperty({
    description: 'Indicates if the record is subject to inspection.',
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  inspectableRecord?: boolean;

  @ApiProperty({
    description:
      'Indicates if the record includes Protected Health Information (PHI).',
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  includesPHI?: boolean;

  // --- Existing Control Fields ---

  @ApiProperty({
    description:
      'Set to true if this is a TMF artifact category. At least one of isTMF or isISF must be true.',
    required: true,
  })
  @IsNotEmpty()
  @IsBoolean()
  isTMF: boolean;

  @ApiProperty({
    description:
      'Set to true if this is an ISF artifact category. At least one of isTMF or isISF must be true.',
    required: true,
  })
  @IsNotEmpty()
  @IsBoolean()
  isISF: boolean;

  @ApiProperty({
    description: 'TMF core status.',
    enum: TMFCore,
    required: false,
  })
  @IsOptional()
  @IsEnum(TMFCore)
  tmfCore?: TMFCore;

  @ApiProperty({
    description: 'Investigator initiated trial study artifact type.',
    enum: InvestigatorInitiatedStudyArtifacts,
    required: false,
  })
  @IsOptional()
  @IsEnum(InvestigatorInitiatedStudyArtifacts)
  iitStudyArtifacts?: InvestigatorInitiatedStudyArtifacts;

  @ApiProperty({
    description: 'Origin of the artifact category.',
    enum: OriginType,
    required: false,
    nullable: true,
  })
  @IsOptional()
  @IsEnum(OriginType)
  origin?: OriginType | null;

  @ApiProperty({
    description: 'Is the category active.',
    required: false,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiProperty({
    description: 'ID of the parent Artifact Category Version.',
    required: true,
  })
  @IsUUID()
  categoryVersionId: string;
}

export class CategoryVersionInfoDto {
  @ApiProperty({
    description: 'Effective date',
    required: true,
  })
  @IsNotEmpty()
  @IsDate()
  @Type(() => Date)
  effectiveDate: Date;

  @ApiProperty({
    description: 'Version number',
    required: true,
  })
  @IsNotEmpty()
  @IsNumber()
  version: number;

  @ApiProperty({
    description: 'Notes about this version',
    required: false,
  })
  @IsOptional()
  @IsString()
  notes?: string;
}
