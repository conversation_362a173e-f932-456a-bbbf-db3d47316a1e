import { Test, TestingModule } from '@nestjs/testing';
import { AiPromptVariableService } from './ai-prompt-variable.service';

describe('AiPromptVariableService', () => {
  let service: AiPromptVariableService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [AiPromptVariableService],
    }).compile();

    service = module.get<AiPromptVariableService>(AiPromptVariableService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
