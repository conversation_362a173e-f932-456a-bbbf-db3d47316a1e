import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
  ParseUUIDPipe,
  Put,
} from '@nestjs/common';
import { ArtifactIsfRefModelService } from './artifact-isf-ref-model.service';
import {
  IsfRefModelCreateDto,
  IsfRefModelUpdateDto,
} from './dto/create-artifact-isf-ref-model.dto';
import { UpdateArtifactIsfRefModelDto } from './dto/update-artifact-isf-ref-model.dto';
import { ApiResponse, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from 'src/auth/jwt-auth.guard';
import { RolesGuard } from 'src/auth/roles/roles.guard';
import { InjectPinoLogger, PinoLogger } from 'nestjs-pino';
import { ArtifactIsfRefModel } from 'src/models/entities/artifact-isf-ref-model.entity';
import { Roles } from 'src/auth/roles/roles.decorator';
import { PaginationDto } from 'src/models/dto/pagination.dto';
import { FilterIsfRefModelDto } from 'src/models/dto/filters.dto';
import { MetadataDto } from 'src/models/dto/meta-data.dto';

@ApiTags('Artifact Isf Ref Model')
@UseGuards(JwtAuthGuard, RolesGuard)
@Controller({
  path: ['admin/artifact-isf-ref-model'],
  version: '1',
})
export class ArtifactIsfRefModelController {
  constructor(
    private readonly artifactIsfRefModelService: ArtifactIsfRefModelService,
    @InjectPinoLogger(ArtifactIsfRefModelController.name)
    protected readonly logger: PinoLogger,
  ) {}

  @Post()
  @Roles('admin')
  @ApiResponse({
    status: 201,
    description: 'The isf ref model has been successfully created.',
    type: ArtifactIsfRefModel,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - validation error',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - insufficient permissions',
  })
  async createIsfRefModel(
    @Body() refModelCreateDto: IsfRefModelCreateDto,
  ): Promise<ArtifactIsfRefModel> {
    try {
      return await this.artifactIsfRefModelService.createIsfRefModel(
        refModelCreateDto,
      );
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  @Get()
  @Roles('admin')
  @ApiResponse({
    status: 200,
    description: 'Get a paginated list of isf ref model.',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - validation error',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - insufficient permissions',
  })
  async getIsfRefModel(
    @Query() paginationDto: PaginationDto<FilterIsfRefModelDto>,
  ): Promise<{ results: ArtifactIsfRefModel[]; metadata: MetadataDto }> {
    try {
      return await this.artifactIsfRefModelService.getIsfRefModel(
        paginationDto,
      );
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  @Get(':modelId')
  @Roles('admin')
  @ApiResponse({
    status: 200,
    description: 'Get a isf ref model by ID.',
    type: ArtifactIsfRefModel,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - validation error',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - insufficient permissions',
  })
  async getTmfRefModelById(
    @Param('modelId', ParseUUIDPipe) modelId: string,
  ): Promise<ArtifactIsfRefModel> {
    try {
      return await this.artifactIsfRefModelService.getIsfRefModelById(modelId);
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  @Put(':modelId')
  @Roles('admin')
  @ApiResponse({
    status: 200,
    description: 'Update a isf ref model successfully',
    type: ArtifactIsfRefModel,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - validation error',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - insufficient permissions',
  })
  async updateIsfRefModel(
    @Param('modelId', ParseUUIDPipe) modelId: string,
    @Body() refModelUpdateDto: IsfRefModelUpdateDto,
  ): Promise<ArtifactIsfRefModel> {
    try {
      return await this.artifactIsfRefModelService.updateIsfRefModel(
        modelId,
        refModelUpdateDto,
      );
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  @Delete(':modelId')
  @Roles('admin')
  @ApiResponse({
    status: 200,
    description: 'Delete a ref model',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - validation error',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description:
      'Attempt to delete a ref model that links to an artifact category.',
  })
  async deleteRefModel(@Param('modelId') modelId: string) {
    try {
      await this.artifactIsfRefModelService.deleteRefModel(modelId);
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }
}
