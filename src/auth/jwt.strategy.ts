/* eslint-disable prettier/prettier */
import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import * as jwksRsa from 'jwks-rsa';
import { ClerkService } from 'src/clerk/clerk.service';

export interface AuthenticatedUser {
  userId: string;
  publicMetadata: { [key: string]: any };
}

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(
    private clerkService: ClerkService,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKeyProvider: jwksRsa.passportJwtSecret({
        cache: true,
        rateLimit: true,
        jwksRequestsPerMinute: 5,
        jwksUri:
          'https://aware-peacock-2.clerk.accounts.dev/.well-known/jwks.json',
      }),
      algorithms: ['RS256'],
      issuer: process.env.CLERK_ISSUER,
    });
  }

  async validate(payload: any): Promise<AuthenticatedUser> {
    const userId = payload.sub;
    const publicMetadata = await this.clerkService.getMetadata(userId)
    return {
      userId: userId,
      publicMetadata: publicMetadata || {},
    };
  }
}
