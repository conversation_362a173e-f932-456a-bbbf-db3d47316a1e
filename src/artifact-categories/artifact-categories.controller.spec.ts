import { Test, TestingModule } from '@nestjs/testing';
import { ArtifactCategoriesController } from './artifact-categories.controller';
import { ArtifactCategoriesService } from './artifact-categories.service';

describe('ArtifactCategoriesController', () => {
  let controller: ArtifactCategoriesController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ArtifactCategoriesController],
      providers: [ArtifactCategoriesService],
    }).compile();

    controller = module.get<ArtifactCategoriesController>(
      ArtifactCategoriesController,
    );
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
