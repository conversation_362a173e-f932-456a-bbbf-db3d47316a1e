import { Test, TestingModule } from '@nestjs/testing';
import { ArtifactIsfRefModelService } from './artifact-isf-ref-model.service';

describe('ArtifactIsfRefModelService', () => {
  let service: ArtifactIsfRefModelService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ArtifactIsfRefModelService],
    }).compile();

    service = module.get<ArtifactIsfRefModelService>(
      ArtifactIsfRefModelService,
    );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
