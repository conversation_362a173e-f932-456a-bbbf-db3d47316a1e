import { Module } from '@nestjs/common';
import { ArtifactTmfRefModelService } from './artifact-tmf-ref-model.service';
import { ArtifactTmfRefModelController } from './artifact-tmf-ref-model.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ArtifactTmfRefModel } from 'src/models/entities/artifact-tmf-ref-model.entity';
import { ArtifactCategories } from 'src/models/entities/artifact-category.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([ArtifactTmfRefModel, ArtifactCategories]),
  ],
  controllers: [ArtifactTmfRefModelController],
  providers: [ArtifactTmfRefModelService],
})
export class ArtifactTmfRefModelModule {}
