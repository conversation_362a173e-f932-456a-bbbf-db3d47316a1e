import { Test, TestingModule } from '@nestjs/testing';
import { AiPromptTemplateController } from './ai-prompt-template.controller';
import { AiPromptTemplateService } from './ai-prompt-template.service';

describe('AiPromptTemplateController', () => {
  let controller: AiPromptTemplateController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AiPromptTemplateController],
      providers: [AiPromptTemplateService],
    }).compile();

    controller = module.get<AiPromptTemplateController>(
      AiPromptTemplateController,
    );
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
