import {
  BadRequestException,
  ConflictException,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { UpdateArtifactIsfRefModelDto } from './dto/update-artifact-isf-ref-model.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { ArtifactIsfRefModel } from 'src/models/entities/artifact-isf-ref-model.entity';
import { DataSource, ILike, Repository } from 'typeorm';
import { InjectPinoLogger, PinoLogger } from 'nestjs-pino';
import {
  IsfRefModelCreateDto,
  IsfRefModelUpdateDto,
} from './dto/create-artifact-isf-ref-model.dto';
import { PaginationDto } from 'src/models/dto/pagination.dto';
import { FilterIsfRefModelDto } from 'src/models/dto/filters.dto';
import { MetadataDto } from 'src/models/dto/meta-data.dto';
import { ArtifactCategories } from 'src/models/entities/artifact-category.entity';

@Injectable()
export class ArtifactIsfRefModelService {
  constructor(
    @InjectRepository(ArtifactIsfRefModel)
    private refModelRepository: Repository<ArtifactIsfRefModel>,
    @InjectRepository(ArtifactCategories)
    private categoryRepo: Repository<ArtifactCategories>,
    @InjectPinoLogger(ArtifactIsfRefModelService.name)
    protected readonly logger: PinoLogger,
    protected dataSource: DataSource,
  ) {}

  async createIsfRefModel(
    refModelCreateDto: IsfRefModelCreateDto,
  ): Promise<ArtifactIsfRefModel> {
    try {
      const refModel = await this.refModelRepository.findOne({
        where: { isfRefModel: refModelCreateDto.isfRefModel },
      });
      if (refModel) {
        throw new BadRequestException('Ref Model already existed.');
      }
      const newRefModel = this.refModelRepository.create(refModelCreateDto);
      return this.refModelRepository.save(newRefModel);
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async getIsfRefModel(
    paginationDto: PaginationDto<FilterIsfRefModelDto>,
  ): Promise<{ results: ArtifactIsfRefModel[]; metadata: MetadataDto }> {
    try {
      const defaultOrderBy = 'id';
      const orderBy = paginationDto.orderBy || defaultOrderBy;
      const orderDirection =
        paginationDto.orderDirection || ('ASC' as 'ASC' | 'DESC');

      const options = {
        take: paginationDto.take,
        skip: (paginationDto.page - 1) * paginationDto.take,
        where: {
          ...(paginationDto.filter?.isfRefModel && {
            isfRefModel: ILike(`%${paginationDto.filter.isfRefModel}%`),
          }),
        },
        order: { [orderBy]: orderDirection },
      };

      const [results, count] = await this.refModelRepository.findAndCount(
        options,
      );

      const metadata: MetadataDto = {
        totalCount: count,
        itemCount: results.length,
        itemsPerPage: paginationDto.take,
        totalPages: Math.ceil(count / paginationDto.take),
        currentPage: paginationDto.page,
      };

      return { results, metadata };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async getIsfRefModelById(modelId: string): Promise<ArtifactIsfRefModel> {
    try {
      const refModel = await this.refModelRepository.findOne({
        where: { id: modelId },
      });
      if (!refModel) {
        throw new NotFoundException('Ref Model not found.');
      }
      return refModel;
    } catch (error) {
      if (
        error instanceof BadRequestException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new InternalServerErrorException(
        'An unexpected error occurred while getting ref model by id.',
      );
    }
  }

  async updateIsfRefModel(
    modelId: string,
    refModelUpdateDto: IsfRefModelUpdateDto,
  ): Promise<ArtifactIsfRefModel> {
    try {
      const refModel = await this.refModelRepository.findOne({
        where: { id: modelId },
      });

      if (!refModel) {
        throw new NotFoundException('Ref Model not found');
      }

      const existingRefModel = await this.refModelRepository.findOne({
        where: { isfRefModel: refModelUpdateDto.isfRefModel },
      });
      if (existingRefModel && existingRefModel.id !== refModel.id) {
        throw new BadRequestException(
          `Ref Model with name ${refModelUpdateDto.isfRefModel} already existed.`,
        );
      }

      refModel.isfRefModel = refModelUpdateDto.isfRefModel;
      if (refModelUpdateDto?.description) {
        refModel.description = refModelUpdateDto.description;
      }
      const refModelUpdated = await this.refModelRepository.save(refModel);
      return refModelUpdated;
    } catch (error) {
      throw error;
    }
  }

  async deleteRefModel(modelId: string) {
    try {
      const linkedCategoryCount = await this.categoryRepo.count({
        where: { isfRefModelId: modelId },
      });

      if (linkedCategoryCount > 0) {
        throw new ConflictException(
          'Cannot delete models that link to an Artifact Category',
        );
      }
      const refModel = await this.getIsfRefModelById(modelId);
      return await this.refModelRepository.softRemove(refModel);
    } catch (error) {
      if (
        !(
          error instanceof NotFoundException ||
          error instanceof BadRequestException ||
          error instanceof ConflictException
        )
      ) {
        this.logger.error(
          `Failed to remove model ${modelId}: ${error.message}`,
          error.stack,
        );
        throw error;
      }
      throw error;
    }
  }
}
