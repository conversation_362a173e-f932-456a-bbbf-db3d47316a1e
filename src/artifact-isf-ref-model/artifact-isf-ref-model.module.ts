import { Modu<PERSON> } from '@nestjs/common';
import { ArtifactIsfRefModelService } from './artifact-isf-ref-model.service';
import { ArtifactIsfRefModelController } from './artifact-isf-ref-model.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ArtifactIsfRefModel } from 'src/models/entities/artifact-isf-ref-model.entity';
import { ArtifactCategories } from 'src/models/entities/artifact-category.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([ArtifactIsfRefModel, ArtifactCategories]),
  ],
  controllers: [ArtifactIsfRefModelController],
  providers: [ArtifactIsfRefModelService],
})
export class ArtifactIsfRefModelModule {}
