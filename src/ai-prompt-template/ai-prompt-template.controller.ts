import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
  ParseUUIDPipe,
} from '@nestjs/common';
import { AiPromptTemplateService } from './ai-prompt-template.service';
import { CreateAiPromptTemplateDto } from './dto/create-ai-prompt-template.dto';
import { UpdateAiPromptTemplateDto } from './dto/update-ai-prompt-template.dto';
import { JwtAuthGuard } from 'src/auth/jwt-auth.guard';
import { RolesGuard } from 'src/auth/roles/roles.guard';
import { Roles } from 'src/auth/roles/roles.decorator';
import {
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import {
  AiModelProvider,
  AiPromptTemplate,
} from 'src/models/entities/ai-prompt-template.entity';
import { PaginationDto } from 'src/models/dto/pagination.dto';
import { AiPromptTemplateQueryDto } from 'src/models/dto/filters.dto';
import { ModelsResponse } from './dto/model-response.dto';

@ApiTags('AI Prompt Template')
@Controller({
  path: 'admin/ai-prompt-template',
  version: '1',
})
@UseGuards(JwtAuthGuard, RolesGuard)
export class AiPromptTemplateController {
  constructor(
    private readonly aiPromptTemplateService: AiPromptTemplateService,
  ) {}

  @Post()
  @Roles('admin')
  @ApiOperation({ summary: 'Create a new AI prompt template' })
  @ApiResponse({
    status: 201,
    description: 'The AI prompt template has been successfully created.',
    type: AiPromptTemplate,
  })
  async create(
    @Body() createAiPromptTemplateDto: CreateAiPromptTemplateDto,
  ): Promise<AiPromptTemplate> {
    return this.aiPromptTemplateService.create(createAiPromptTemplateDto);
  }

  @Get()
  @Roles('admin')
  @ApiOperation({ summary: 'Get all AI prompt templates' })
  @ApiResponse({
    status: 200,
    description: 'List of all AI prompt templates.',
    type: [AiPromptTemplate],
  })
  async findAll(@Query() dto: PaginationDto<AiPromptTemplateQueryDto>) {
    return this.aiPromptTemplateService.findAll(dto);
  }

  @Get('models')
  @Roles('admin')
  @ApiOperation({ summary: 'Get models for selected provider' })
  @ApiQuery({
    name: 'provider',
    enum: AiModelProvider,
    description: 'Provider needs models',
  })
  @ApiResponse({
    status: 200,
    description: 'All models for selected provider',
    type: ModelsResponse,
  })
  @ApiResponse({
    status: 404,
    description: 'Models not found',
  })
  getModelsForProvider(
    @Query('provider') provider: AiModelProvider,
  ): ModelsResponse {
    try {
      return this.aiPromptTemplateService.getModelsForProvider(provider);
    } catch (error) {
      throw error;
    }
  }

  @Get(':id')
  @Roles('admin')
  findOne(@Param('id') id: string) {
    return this.aiPromptTemplateService.findOne(id);
  }

  @Patch(':id')
  @Roles('admin')
  @ApiOperation({ summary: 'Update an existing AI prompt template' })
  @ApiParam({
    name: 'id',
    description: 'UUID of the AI prompt template',
  })
  @ApiResponse({
    status: 200,
    description: 'The AI prompt template has been successfully updated.',
    type: AiPromptTemplate,
  })
  @ApiResponse({
    status: 404,
    description: 'AI prompt template not found.',
  })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateAiPromptTemplateDto: UpdateAiPromptTemplateDto,
  ): Promise<AiPromptTemplate> {
    return this.aiPromptTemplateService.update(id, updateAiPromptTemplateDto);
  }

  @Patch(':id/activate')
  @Roles('admin')
  @ApiOperation({ summary: 'Activate a specific AI prompt template' })
  @ApiParam({
    name: 'id',
    description: 'UUID of the AI prompt template to activate',
  })
  @ApiResponse({
    status: 200,
    description: 'The AI prompt template has been successfully activated.',
    type: AiPromptTemplate,
  })
  @ApiResponse({
    status: 404,
    description: 'AI prompt template not found.',
  })
  async activatePrompt(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<AiPromptTemplate> {
    return await this.aiPromptTemplateService.activatePrompt(id);
  }

  @Delete(':id')
  @Roles('admin')
  @ApiOperation({ summary: 'Delete an AI prompt template' })
  @ApiParam({
    name: 'id',
    description: 'UUID of the AI prompt template to delete',
  })
  @ApiResponse({
    status: 200,
    description: 'The AI prompt template has been successfully deleted.',
    type: Boolean,
  })
  @ApiResponse({
    status: 400,
    description: 'Cannot delete an active prompt template.',
  })
  @ApiResponse({
    status: 404,
    description: 'AI prompt template not found.',
  })
  async delete(@Param('id', ParseUUIDPipe) id: string): Promise<boolean> {
    return await this.aiPromptTemplateService.delete(id);
  }
}
