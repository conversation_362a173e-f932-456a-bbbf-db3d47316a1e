import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddArtifactCategory1753925892261 implements MigrationInterface {
  name = 'AddArtifactCategory1753925892261';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."artifact_categories_tmfcore_enum" AS ENUM('recommended', 'core')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."artifact_categories_iitstudyartifacts_enum" AS ENUM('mandatory', 'dependent', 'recommended')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."artifact_categories_origin_enum" AS ENUM('to_ISF', 'to_TMF')`,
    );
    await queryRunner.query(
      `CREATE TABLE "artifact_categories" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdDate" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "lastUpdatedDate" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deletedDate" TIMESTAMP WITH TIME ZONE, "tmfZoneNumber" character varying, "tmfZoneName" character varying, "tmfSectionNumber" character varying, "tmfSectionName" character varying, "tmfRecordGroupNumber" character varying, "tmfRecordGroupName" character varying, "recordType" character varying NOT NULL, "isfZoneNumber" character varying, "isfZoneName" character varying, "isfSectionNumber" character varying, "isfSectionName" character varying, "isfRecordGroupNumber" character varying, "isfRecordGroupName" character varying, "alternativeNames" character varying, "description" text, "requiresSignature" boolean, "expires" boolean, "inspectableRecord" boolean, "includesPHI" boolean, "tmfRefModelId" uuid, "isfRefModelId" uuid, "tmfCore" "public"."artifact_categories_tmfcore_enum", "isTMF" boolean NOT NULL, "isISF" boolean NOT NULL, "iitStudyArtifacts" "public"."artifact_categories_iitstudyartifacts_enum", "isActive" boolean NOT NULL DEFAULT true, "origin" "public"."artifact_categories_origin_enum", "categoryVersionId" uuid, CONSTRAINT "PK_d99c707916d990ebd08e379f7f2" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `ALTER TABLE "artifact_categories" ADD CONSTRAINT "FK_d646ec87b164646a6663f5ebd76" FOREIGN KEY ("tmfRefModelId") REFERENCES "artifact_tmf_ref_model"("id") ON DELETE RESTRICT ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "artifact_categories" ADD CONSTRAINT "FK_7829014e51a542f539052dba214" FOREIGN KEY ("isfRefModelId") REFERENCES "artifact_isf_ref_model"("id") ON DELETE RESTRICT ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "artifact_categories" ADD CONSTRAINT "FK_8b0de671296584287e1082da8f9" FOREIGN KEY ("categoryVersionId") REFERENCES "artifact_category_versions"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "artifact_categories" DROP CONSTRAINT "FK_8b0de671296584287e1082da8f9"`,
    );
    await queryRunner.query(
      `ALTER TABLE "artifact_categories" DROP CONSTRAINT "FK_7829014e51a542f539052dba214"`,
    );
    await queryRunner.query(
      `ALTER TABLE "artifact_categories" DROP CONSTRAINT "FK_d646ec87b164646a6663f5ebd76"`,
    );
    await queryRunner.query(`DROP TABLE "artifact_categories"`);
    await queryRunner.query(
      `DROP TYPE "public"."artifact_categories_origin_enum"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."artifact_categories_iitstudyartifacts_enum"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."artifact_categories_tmfcore_enum"`,
    );
  }
}
