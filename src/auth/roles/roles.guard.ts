/* eslint-disable prettier/prettier */
// In central-config/src/auth/roles.guard.ts
import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { ROLES_KEY } from './roles.decorator';
import { AuthenticatedUser } from '../jwt.strategy';

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredRoles = this.reflector.getAllAndOverride<string[]>(ROLES_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (!requiredRoles) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const user: AuthenticatedUser = request.user;

    if (!user || !user.publicMetadata) {
      return false;
    }
    
    const userIsAdmin = user.publicMetadata.groupType === 'clincove';

    const hasRequiredRole = requiredRoles.some((role) => {
        if (role === 'admin') {
            return userIsAdmin;
        }
        return false;
    });

    if (hasRequiredRole) {
        return true; 
    }
    throw new ForbiddenException('You do not have the required permissions to access this resource.');
  }
}