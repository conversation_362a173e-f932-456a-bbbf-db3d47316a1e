name: Development Environment - DB Migration # TBD

on: workflow_dispatch

jobs:
  migrate:
    runs-on: self-hosted
    if: ${{ github.event_name == 'workflow_dispatch' && github.ref == 'refs/heads/develop' }} # TBD
    environment:
      name: development # TBD
    permissions:
      contents: 'read'
      id-token: 'write'
    env:
      VERSION: ${{ github.ref_type == 'branch' && github.sha || github.ref_name }}
      APPLICATION_NAME: ${{ vars.APPLICATION_NAME }}
      IMAGE_REPOSITORY: ${{ vars.REGISTRY }}/${{ vars.IMAGE_NAME }}-migrate
      DATABASE_HOST: ${{ vars.DATABASE_HOST }}
      DATABASE_USERNAME: ${{ vars.DATABASE_USERNAME }}
      DATABASE_PASSWORD: ${{ secrets.DATABASE_PASSWORD }}
    steps:
      - id: checkout-repo
        name: Checkout repository
        uses: actions/checkout@v4

      - uses: clincove-eng/github-actions/setup-docker@v1
        with:
          service_account: ${{ secrets.ORG_WORKLOAD_IDENTITY_SA_EMAIL }}
          workload_identity_provider: ${{ secrets.ORG_WORKLOAD_IDENTITY_PROVIDER_NAME }}
          registry: ${{ secrets.ORG_ARTIFACT_REGISTRY_BASE_URL }}

      - uses: clincove-eng/github-actions/build-image@v1
        with:
          file: docker/Dockerfile.migration
          image_name: ${{ vars.IMAGE_NAME }}-migrate
          registry: ${{ vars.REGISTRY }}

      - id: replace-template-vars
        name: Replace template variables for helm
        run: envsubst < ci/migrate.tpl.yml > migrate.yml

      - uses: clincove-eng/github-actions/migrate-database@v1
        with:
          application_name: ${{ vars.APPLICATION_NAME }}
          cluster_name: ${{ vars.US_CLUSTER_NAME }}
          location: ${{ vars.US_CLUSTER_LOCATION }}
