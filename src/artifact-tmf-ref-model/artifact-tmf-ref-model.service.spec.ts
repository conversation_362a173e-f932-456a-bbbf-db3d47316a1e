import { Test, TestingModule } from '@nestjs/testing';
import { ArtifactTmfRefModelService } from './artifact-tmf-ref-model.service';

describe('ArtifactTmfRefModelService', () => {
  let service: ArtifactTmfRefModelService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ArtifactTmfRefModelService],
    }).compile();

    service = module.get<ArtifactTmfRefModelService>(
      ArtifactTmfRefModelService,
    );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
