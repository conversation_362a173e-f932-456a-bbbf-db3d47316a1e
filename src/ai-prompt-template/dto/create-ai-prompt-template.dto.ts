/* eslint-disable prettier/prettier */
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsString, IsNotEmpty, IsOptional, IsBoolean, IsEnum, IsArray, ValidateNested, IsObject, IsNumber, Min } from 'class-validator';
import { AiPromptTemplateKey, AiModelProvider, ChatMessage, ChatMessagePart, GenerationConfig } from 'src/models/entities/ai-prompt-template.entity';


class ChatMessagePartDto implements ChatMessagePart {
  @ApiProperty({
    description: 'Text content of the message part. Can be an EJS template.',
    example: 'Analyze: <%= document.content %>',
  })
  @IsString()
  text: string;
}


export type ChatRoleDto = 'user' | 'model' | 'system';
class ChatMessageDto implements ChatMessage {
  @ApiProperty({
    description: 'Role of the message sender',
    enum: ['user', 'model', 'system'],
  })
  @IsEnum(['user', 'model', 'system'] as const)
  role: ChatRoleDto;

  @ApiProperty({
    description: 'Parts of the message, each containing text (EJS template)',
    type: [ChatMessagePartDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ChatMessagePartDto)
  parts: ChatMessagePartDto[];

  @ApiPropertyOptional({
    description: 'Is the message cacheable?',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  isCacheable?: boolean;
}

class GenerationConfigDto implements GenerationConfig {
  @ApiPropertyOptional({ description: 'Temperature for generation (0-1)', example: 0.7 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  temperature?: number;

  @ApiPropertyOptional({ description: 'Top P for generation', example: 0.8 })
  @IsOptional()
  @IsNumber() // Or IsNumber
  topP?: number;

  @ApiPropertyOptional({ description: 'Top K for generation', example: 40 })
  @IsOptional()
  @IsNumber()
  topK?: number;
}
export class CreateAiPromptTemplateDto {
  @ApiProperty({
    description: 'Name of the prompt template',
    example: 'Protocol Explorer - Main Analysis v1',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiPropertyOptional({ description: 'Description of what the prompt template does' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description: 'Whether this prompt template is currently active',
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiProperty({
    description: 'Key identifying the purpose/feature of this prompt template',
    enum: AiPromptTemplateKey,
    example: AiPromptTemplateKey.PROTOCOL_EXPLORER,
  })
  @IsEnum(AiPromptTemplateKey)
  key: AiPromptTemplateKey;

  @ApiProperty({
    description: 'EJS template content as an array of chat messages',
    type: [ChatMessageDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ChatMessageDto)
  @IsNotEmpty() // A prompt template must have content
  templateContent: ChatMessageDto[]; // Renamed from 'chatHistory'

  @ApiPropertyOptional({
    description: 'Configuration for the AI model generation',
    type: GenerationConfigDto,
    default: { temperature: 0, topP: 0.8, topK: 40 },
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => GenerationConfigDto)
  generationConfig?: GenerationConfigDto;

  @ApiProperty({
    description: 'The AI model provider to use for this template',
    enum: AiModelProvider,
    example: AiModelProvider.GEMINI,
  })
  @IsEnum(AiModelProvider)
  modelProvider: AiModelProvider;

  @ApiProperty({ description: 'The specific AI model to use', example: 'gemini-1.5-pro-latest' })
  @IsString()
  @IsNotEmpty()
  model: string;

}


