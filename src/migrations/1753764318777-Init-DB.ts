import { MigrationInterface, QueryRunner } from 'typeorm';

export class InitDB1753764318777 implements MigrationInterface {
  name = 'InitDB1753764318777';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "artifact_isf_ref_model" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdDate" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "lastUpdatedDate" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "isfRefModel" character varying NOT NULL, "description" text, CONSTRAINT "UQ_e6ca7c10aa1a69be205e68c7152" UNIQUE ("isfRefModel"), CONSTRAINT "PK_59a18edfcfac086f1be504756c1" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "artifact_category_versions" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdDate" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "lastUpdatedDate" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "version" integer NOT NULL, "effectiveDate" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "notes" text, CONSTRAINT "PK_0d68a8da4560a93c39dca11465a" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."ai_prompt_template_key_enum" AS ENUM('protocolExplorer', 'isfArtifactCategory', 'tmfArtifactCategory')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."ai_prompt_template_modelprovider_enum" AS ENUM('gemini', 'openai')`,
    );
    await queryRunner.query(
      `CREATE TABLE "ai_prompt_template" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdDate" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "lastUpdatedDate" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "name" character varying(255) NOT NULL, "description" text, "isActive" boolean NOT NULL DEFAULT true, "version" integer NOT NULL DEFAULT '1', "key" "public"."ai_prompt_template_key_enum" NOT NULL, "templateContent" jsonb NOT NULL, "generationConfig" jsonb DEFAULT '{"temperature":0,"topP":0.8,"topK":40}'::jsonb, "modelProvider" "public"."ai_prompt_template_modelprovider_enum" NOT NULL, "model" character varying(100) NOT NULL, CONSTRAINT "PK_34dfc517cdb6a513bf007eebf03" PRIMARY KEY ("id")); COMMENT ON COLUMN "ai_prompt_template"."templateContent" IS 'Array of chat messages with role and EJS template parts'`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."ai_prompt_cache_status_enum" AS ENUM('active', 'expired', 'invalidated')`,
    );
    await queryRunner.query(
      `CREATE TABLE "ai_prompt_cache" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdDate" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "lastUpdatedDate" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "name" character varying(255) NOT NULL, "cacheContentId" character varying(255) NOT NULL, "cachedAt" TIMESTAMP WITH TIME ZONE NOT NULL, "isPermanent" boolean NOT NULL DEFAULT false, "expiresAt" TIMESTAMP WITH TIME ZONE, "lastAccessed" TIMESTAMP WITH TIME ZONE, "status" "public"."ai_prompt_cache_status_enum" NOT NULL DEFAULT 'active', "metadata" jsonb, CONSTRAINT "PK_7297a84cc322d90aec3d0e03900" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_db92c8c728e13a364834f39671" ON "ai_prompt_cache" ("name") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_39b5ad00fd411822aee19b511b" ON "ai_prompt_cache" ("cacheContentId") `,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."ai_prompt_variable_type_enum" AS ENUM('string', 'json', 'array', 'number', 'boolean')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."ai_prompt_variable_resolverfunction_enum" AS ENUM('resolveProtocolContent')`,
    );
    await queryRunner.query(
      `CREATE TABLE "ai_prompt_variable" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdDate" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "lastUpdatedDate" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "key" text NOT NULL, "label" text, "description" text, "type" "public"."ai_prompt_variable_type_enum" NOT NULL, "computed" boolean NOT NULL DEFAULT false, "fallbackValue" jsonb, "published" boolean NOT NULL DEFAULT false, "isActive" boolean NOT NULL DEFAULT true, "resolverFunction" "public"."ai_prompt_variable_resolverfunction_enum", CONSTRAINT "UQ_ca524e13229663a449a6ec23a1a" UNIQUE ("key"), CONSTRAINT "PK_d78f10375b49bf257aeda27d552" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_ca524e13229663a449a6ec23a1" ON "ai_prompt_variable" ("key") `,
    );
    await queryRunner.query(
      `CREATE TABLE "artifact_tmf_ref_model" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdDate" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "lastUpdatedDate" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "tmfRefModel" character varying NOT NULL, "description" text, CONSTRAINT "PK_1e65a876475e6ee30668dd483f9" PRIMARY KEY ("id"))`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "artifact_tmf_ref_model"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_ca524e13229663a449a6ec23a1"`,
    );
    await queryRunner.query(`DROP TABLE "ai_prompt_variable"`);
    await queryRunner.query(
      `DROP TYPE "public"."ai_prompt_variable_resolverfunction_enum"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."ai_prompt_variable_type_enum"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_39b5ad00fd411822aee19b511b"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_db92c8c728e13a364834f39671"`,
    );
    await queryRunner.query(`DROP TABLE "ai_prompt_cache"`);
    await queryRunner.query(`DROP TYPE "public"."ai_prompt_cache_status_enum"`);
    await queryRunner.query(`DROP TABLE "ai_prompt_template"`);
    await queryRunner.query(
      `DROP TYPE "public"."ai_prompt_template_modelprovider_enum"`,
    );
    await queryRunner.query(`DROP TYPE "public"."ai_prompt_template_key_enum"`);
    await queryRunner.query(`DROP TABLE "artifact_category_versions"`);
    await queryRunner.query(`DROP TABLE "artifact_isf_ref_model"`);
  }
}
