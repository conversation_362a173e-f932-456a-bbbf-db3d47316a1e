import { MigrationInterface, QueryRunner } from 'typeorm';

export class SetRefModelNotNull1754032141682 implements MigrationInterface {
  name = 'SetRefModelNotNull1754032141682';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "artifact_categories" DROP CONSTRAINT "FK_d646ec87b164646a6663f5ebd76"`,
    );
    await queryRunner.query(
      `ALTER TABLE "artifact_categories" DROP CONSTRAINT "FK_7829014e51a542f539052dba214"`,
    );
    await queryRunner.query(
      `ALTER TABLE "artifact_categories" ALTER COLUMN "tmfRefModelId" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "artifact_categories" ALTER COLUMN "isfRefModelId" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "artifact_categories" ADD CONSTRAINT "FK_d646ec87b164646a6663f5ebd76" FOREIGN KEY ("tmfRefModelId") REFERENCES "artifact_tmf_ref_model"("id") ON DELETE RESTRICT ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "artifact_categories" ADD CONSTRAINT "FK_7829014e51a542f539052dba214" FOREIGN KEY ("isfRefModelId") REFERENCES "artifact_isf_ref_model"("id") ON DELETE RESTRICT ON UPDATE CASCADE`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "artifact_categories" DROP CONSTRAINT "FK_7829014e51a542f539052dba214"`,
    );
    await queryRunner.query(
      `ALTER TABLE "artifact_categories" DROP CONSTRAINT "FK_d646ec87b164646a6663f5ebd76"`,
    );
    await queryRunner.query(
      `ALTER TABLE "artifact_categories" ALTER COLUMN "isfRefModelId" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "artifact_categories" ALTER COLUMN "tmfRefModelId" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "artifact_categories" ADD CONSTRAINT "FK_7829014e51a542f539052dba214" FOREIGN KEY ("isfRefModelId") REFERENCES "artifact_isf_ref_model"("id") ON DELETE RESTRICT ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "artifact_categories" ADD CONSTRAINT "FK_d646ec87b164646a6663f5ebd76" FOREIGN KEY ("tmfRefModelId") REFERENCES "artifact_tmf_ref_model"("id") ON DELETE RESTRICT ON UPDATE CASCADE`,
    );
  }
}
