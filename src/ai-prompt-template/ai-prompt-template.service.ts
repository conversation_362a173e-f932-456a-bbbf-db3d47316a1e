import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { CreateAiPromptTemplateDto } from './dto/create-ai-prompt-template.dto';
import { UpdateAiPromptTemplateDto } from './dto/update-ai-prompt-template.dto';
import { PaginationDto } from 'src/models/dto/pagination.dto';
import { AiPromptTemplateQueryDto } from 'src/models/dto/filters.dto';
import {
  AiModelProvider,
  AiPromptTemplate,
  AiPromptTemplateKey,
} from 'src/models/entities/ai-prompt-template.entity';
import { MetadataDto } from 'src/models/dto/meta-data.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { InjectPinoLogger, PinoLogger } from 'nestjs-pino';
import { ModelsResponse } from './dto/model-response.dto';
import { ModelsForGemini, ModelsForOpenAi } from './enums/ai-model.enum';

@Injectable()
export class AiPromptTemplateService {
  constructor(
    @InjectRepository(AiPromptTemplate)
    private aiPromptTemplateRepository: Repository<AiPromptTemplate>,
    @InjectPinoLogger(AiPromptTemplateService.name)
    private readonly logger: PinoLogger,
  ) {}

  getModelsForProvider(provider: AiModelProvider): ModelsResponse {
    try {
      let modelKeys: any;
      if (provider === AiModelProvider.GEMINI) {
        modelKeys = Object.values(ModelsForGemini);
      } else if (provider === AiModelProvider.OPENAI) {
        modelKeys = Object.values(ModelsForOpenAi);
      }
      return { results: modelKeys };
    } catch (error) {
      this.logger.error({ err: error }, 'Error fetching models.');
      throw error;
    }
  }

  async create(
    createAiPromptTemplateDto: CreateAiPromptTemplateDto,
  ): Promise<AiPromptTemplate> {
    this.logger.debug(
      { createAiPromptTemplateDto },
      'Attempting to create a new AI Prompt Template.',
    );

    try {
      const latestPrompt = await this.aiPromptTemplateRepository.findOne({
        where: { key: createAiPromptTemplateDto.key },
        order: { version: 'DESC' },
      });

      const nextVersion = latestPrompt ? latestPrompt.version + 1 : 1;

      const newPromptData: Partial<AiPromptTemplate> = {
        ...createAiPromptTemplateDto,
        version: nextVersion,
      };

      const aiPromptTemplate =
        this.aiPromptTemplateRepository.create(newPromptData);

      // If this new prompt is set to active (or defaults to active),
      // deactivate all other prompts of the same 'key'.
      if (aiPromptTemplate.isActive !== false) {
        // Handles undefined or true
        this.logger.info(
          `New prompt template for key '${aiPromptTemplate.key}' is active. Deactivating other versions.`,
        );
        await this.deactivateOtherPromptsOfKey(aiPromptTemplate.key);
        aiPromptTemplate.isActive = true;
      }

      const savedPromptTemplate = await this.aiPromptTemplateRepository.save(
        aiPromptTemplate,
      );
      this.logger.info(
        {
          id: savedPromptTemplate.id,
          key: savedPromptTemplate.key,
          version: savedPromptTemplate.version,
        },
        'Successfully created AI Prompt Template.',
      );
      return savedPromptTemplate;
    } catch (error) {
      this.logger.error(
        { err: error, createAiPromptTemplateDto },
        'Error creating AI Prompt Template.',
      );
      // Consider more specific error handling or rethrowing a custom exception
      throw new InternalServerErrorException(
        'Failed to create AI Prompt Template.',
      );
    }
  }

  async findAll(
    dto: PaginationDto<AiPromptTemplateQueryDto>,
  ): Promise<{ results: AiPromptTemplate[]; metadata: MetadataDto }> {
    const { take, skip, orderBy, orderDirection, filter } = dto;
    const queryBuilder =
      this.aiPromptTemplateRepository.createQueryBuilder('promptTemplate');

    if (filter?.key) {
      queryBuilder.andWhere('promptTemplate.key = :key', { key: filter.key });
    }

    if (filter?.isActive !== undefined) {
      queryBuilder.andWhere('promptTemplate.isActive = :isActive', {
        isActive: filter.isActive,
      });
    }

    if (filter?.version) {
      queryBuilder.andWhere('promptTemplate.version = :version', {
        version: filter.version,
      });
    }

    if (filter?.name) {
      queryBuilder.andWhere('promptTemplate.name ILIKE :name', {
        name: `%${filter.name}%`,
      });
    }

    if (filter?.modelProvider) {
      queryBuilder.andWhere('promptTemplate.modelProvider = :modelProvider', {
        modelProvider: filter.modelProvider,
      });
    }

    if (filter?.model) {
      queryBuilder.andWhere('promptTemplate.model ILIKE :model', {
        model: `%${filter.model}%`,
      });
    }

    queryBuilder.orderBy('promptTemplate.isActive', 'DESC');

    const validOrderByFields = [
      'name',
      'key',
      'version',
      'modelProvider',
      'model',
      'createdDate',
      'lastUpdatedDate',
      'isActive',
    ];
    const safeOrderBy =
      orderBy && validOrderByFields.includes(orderBy) ? orderBy : 'key';
    // queryBuilder.orderBy('promptTemplate.version', 'DESC');
    queryBuilder.addOrderBy('promptTemplate.version', 'DESC');
    queryBuilder.addOrderBy(
      `promptTemplate.${safeOrderBy}`,
      orderDirection?.toUpperCase() === 'ASC' ||
        orderDirection?.toUpperCase() === 'DESC'
        ? (orderDirection.toUpperCase() as 'ASC' | 'DESC')
        : 'ASC', // Default to ASC
    );

    queryBuilder.skip(skip).take(take);

    this.logger.debug(
      { query: queryBuilder.getQueryAndParameters() },
      'Executing findAll query for AI Prompt Templates.',
    );

    const [promptTemplates, total] = await queryBuilder.getManyAndCount();

    const metadata: MetadataDto = {
      totalCount: total,
      itemCount: promptTemplates.length,
      itemsPerPage: take,
      totalPages: Math.ceil(total / take),
      currentPage: Math.floor(skip / take) + 1,
    };

    return { results: promptTemplates, metadata };
  }

  async findOne(id: string): Promise<AiPromptTemplate> {
    const aiPromptTemplate = await this.aiPromptTemplateRepository.findOne({
      where: { id },
    });

    if (!aiPromptTemplate) {
      throw new NotFoundException(`AI Prompt template with ID ${id} not found`);
    }

    return aiPromptTemplate;
  }

  async update(
    id: string,
    updateAiPromptTemplateDto: UpdateAiPromptTemplateDto,
  ): Promise<AiPromptTemplate> {
    const aiPromptTemplte = await this.findOne(id);

    // If updating to active, deactivate all other prompts of the same type
    if (
      updateAiPromptTemplateDto.isActive === true &&
      !aiPromptTemplte.isActive
    ) {
      await this.deactivateOtherPromptsOfKey(aiPromptTemplte.key);
    }

    Object.assign(aiPromptTemplte, updateAiPromptTemplateDto);
    return await this.aiPromptTemplateRepository.save(aiPromptTemplte);
  }

  async activatePrompt(id: string): Promise<AiPromptTemplate> {
    const aiPromptTemplate = await this.findOne(id);

    await this.deactivateOtherPromptsOfKey(aiPromptTemplate.key);

    aiPromptTemplate.isActive = true;
    return await this.aiPromptTemplateRepository.save(aiPromptTemplate);
  }

  async delete(id: string): Promise<boolean> {
    const aiPromptTemplate = await this.findOne(id);
    if (aiPromptTemplate.isActive) {
      throw new BadRequestException(
        'Cannot delete an active prompt template. Please deactivate it first.',
      );
    }
    try {
      return !!(await this.aiPromptTemplateRepository.softRemove(
        aiPromptTemplate,
      ));
    } catch (e) {
      this.logger.error('Delete AI prompt template fail with error: ', e);
      throw e;
    }
  }

  private async deactivateOtherPromptsOfKey(
    key: AiPromptTemplateKey,
  ): Promise<void> {
    await this.aiPromptTemplateRepository.update(
      { key: key, isActive: true },
      { isActive: false },
    );
    this.logger.info(
      `Deactivated all other active prompt templates for key '${key}'.`,
    );
  }
}
