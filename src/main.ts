import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe, VersioningType } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { DataSource } from 'typeorm';
import { Logger } from 'nestjs-pino';
import * as express from 'express';
import { config as dotenvConfig } from 'dotenv';
import * as qs from 'qs';

// Load environment variables early
dotenvConfig({ path: '.env' });

async function bootstrap() {
  const app = await NestFactory.create(AppModule, {
    bufferLogs: true,
    bodyParser: true,
  });
  app.useGlobalPipes(
    new ValidationPipe({ skipMissingProperties: false, transform: true }),
  );
  app.useLogger(app.get(Logger));
  // app.useGlobalFilters(new AuthFilterException());

  // Increase JSON body parser limit to 5MB
  app.use(express.json({ limit: '5mb' }));
  app.use(express.urlencoded({ extended: true, limit: '5mb' }));

  // Enable URI Versioning
  app.enableVersioning({
    type: VersioningType.URI,
    prefix: 'api/v', // Optional prefix
  });

  const corsOrigins = [...process.env.CORS_ORIGINS.split(',')];
  app.get(Logger).log('Application starting...', new Date().toISOString());
  app.get(Logger).log('Environment:', process.env.NODE_ENV);
  app.get(Logger).log('Database host:', process.env.DATABASE_HOST);
  app.get(Logger).log(corsOrigins);
  app.enableCors({
    credentials: true,
    origin: corsOrigins,
  });

  // Swagger
  const config = new DocumentBuilder()
    .setTitle('Clincove Central Config API')
    .setVersion('1.0')
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        description: 'admin JWT token for authentication',
      },
      'adminJWT',
    )
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/swagger', app, document);

  // Serve Swagger JSON document
  app.use('/api/swagger-json', (req, res) => {
    res.setHeader('Content-Type', 'application/json');
    res.send(document);
  });

  // Handle shutdown gracefully
  app.enableShutdownHooks();
  const cleanup = async () => {
    app.get(Logger).log('Starting graceful shutdown');

    try {
      const dataSource = app.get(DataSource);
      if (dataSource?.isInitialized) {
        // Cancel any pending queries
        await dataSource.query(
          'SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE datname = $1',
          [process.env.DATABASE_NAME],
        );
        // Wait a bit for queries to cancel
        await new Promise((resolve) => setTimeout(resolve, 1000));
        await dataSource.destroy();
        app.get(Logger).log('Database connections closed');
      }

      await app.close();
      app.get(Logger).log('Application closed');

      process.exit(0);
    } catch (error) {
      app.get(Logger).error('Error during shutdown:', error);
      process.exit(1);
    }
  };

  // Handle shutdown signals
  ['SIGTERM', 'SIGINT'].forEach((signal) => {
    process.on(signal, cleanup);
  });

  const expressApp = app.getHttpAdapter().getInstance();
  expressApp.set('query parser', (str) => qs.parse(str));

  await app.listen(3000);
  app.get(Logger).log('Application started on port 3000');
}
bootstrap();
