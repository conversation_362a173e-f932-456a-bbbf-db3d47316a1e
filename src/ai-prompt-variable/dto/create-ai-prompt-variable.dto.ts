import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsEnum, IsBoolean } from 'class-validator';
import {
  PromptVariableType,
  PromptVariableResolver,
} from 'src/models/entities/ai-prompt-variable.entity';

export class CreateAiPromptVariableDto {
  @ApiProperty({
    description: 'Unique key of the variable',
    example: 'protocol.content',
    nullable: false,
  })
  @IsString()
  key: string;

  @ApiPropertyOptional({
    description: 'Label of the variable',
    example: 'Full Protocol Text',
    nullable: true,
  })
  @IsOptional()
  @IsString()
  label?: string | null;

  @ApiPropertyOptional({
    description:
      'Detailed explanation of what this variable contains or how it s derived',
    example: 'This variable contains full protocol text',
    nullable: true,
  })
  @IsOptional()
  @IsString()
  description?: string | null;

  @ApiProperty({
    description: 'Type of this variable',
    example: 'string',
    nullable: false,
  })
  @IsEnum(PromptVariableType)
  type: PromptVariableType;

  @ApiProperty({
    description:
      'If true, its value is dynamically computed by a backend resolver function',
    example: false,
    nullable: false,
  })
  @IsBoolean()
  computed: boolean;

  @ApiProperty({
    description:
      'If true, its value is dynamically computed by a backend resolver function',
    example: true,
    nullable: false,
  })
  @IsBoolean()
  published: boolean;

  @ApiPropertyOptional({
    description: 'Optional fallback value',
    example: {},
    nullable: true,
  })
  @IsOptional()
  fallbackValue?: any;

  @ApiPropertyOptional({
    description:
      'This field used to define which resolver function should be used for this variable',
    example: PromptVariableResolver.RESOLVE_PROTOCOL_CONTENT,
    nullable: true,
  })
  @IsEnum(PromptVariableResolver)
  @IsOptional()
  resolverFunction?: PromptVariableResolver;
}

export class AiPromptVariableUpdateDto extends CreateAiPromptVariableDto {}
