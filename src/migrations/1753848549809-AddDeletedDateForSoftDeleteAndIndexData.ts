import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddDeletedDateForSoftDeleteAndIndexData1753848549809
  implements MigrationInterface
{
  name = 'AddDeletedDateForSoftDeleteAndIndexData1753848549809';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "artifact_isf_ref_model" ADD "deletedDate" TIMESTAMP WITH TIME ZONE`,
    );
    await queryRunner.query(
      `ALTER TABLE "ai_prompt_variable" ADD "deletedDate" TIMESTAMP WITH TIME ZONE`,
    );
    await queryRunner.query(
      `ALTER TABLE "artifact_category_versions" ADD "deletedDate" TIMESTAMP WITH TIME ZONE`,
    );
    await queryRunner.query(
      `ALTER TABLE "artifact_tmf_ref_model" ADD "deletedDate" TIMESTAMP WITH TIME ZONE`,
    );
    await queryRunner.query(
      `ALTER TABLE "ai_prompt_template" ADD "deletedDate" TIMESTAMP WITH TIME ZONE`,
    );
    await queryRunner.query(
      `ALTER TABLE "ai_prompt_cache" ADD "deletedDate" TIMESTAMP WITH TIME ZONE`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_8209456467ad6c85c38fb33fc8" ON "artifact_isf_ref_model" ("deletedDate") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_e7a9e06da1c5448a09b1abe229" ON "artifact_isf_ref_model" ("lastUpdatedDate") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_b519b7b4708a583f4346a19e4c" ON "ai_prompt_variable" ("deletedDate") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_690fcbff1ea33293b998263cd4" ON "ai_prompt_variable" ("lastUpdatedDate") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_5ab028e987f6b4b722007e1c20" ON "artifact_category_versions" ("deletedDate") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_b3ac2387c63462deefcf8bcd5e" ON "artifact_category_versions" ("lastUpdatedDate") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_add58ca77e28403161a66abd0f" ON "artifact_tmf_ref_model" ("deletedDate") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_af67459c8831c6b1d1202a46b7" ON "artifact_tmf_ref_model" ("lastUpdatedDate") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_a4e9d7e941002e383e4fa6c36e" ON "ai_prompt_template" ("deletedDate") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_859b589edc141f3df724a6e151" ON "ai_prompt_template" ("lastUpdatedDate") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_88aedf2977242767e35436630b" ON "ai_prompt_cache" ("deletedDate") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_7c1e8d5b0acdb351ac026cb6dd" ON "ai_prompt_cache" ("lastUpdatedDate") `,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `DROP INDEX "public"."IDX_7c1e8d5b0acdb351ac026cb6dd"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_88aedf2977242767e35436630b"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_859b589edc141f3df724a6e151"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_a4e9d7e941002e383e4fa6c36e"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_af67459c8831c6b1d1202a46b7"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_add58ca77e28403161a66abd0f"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_b3ac2387c63462deefcf8bcd5e"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_5ab028e987f6b4b722007e1c20"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_690fcbff1ea33293b998263cd4"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_b519b7b4708a583f4346a19e4c"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_e7a9e06da1c5448a09b1abe229"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_8209456467ad6c85c38fb33fc8"`,
    );
    await queryRunner.query(
      `ALTER TABLE "ai_prompt_cache" DROP COLUMN "deletedDate"`,
    );
    await queryRunner.query(
      `ALTER TABLE "ai_prompt_template" DROP COLUMN "deletedDate"`,
    );
    await queryRunner.query(
      `ALTER TABLE "artifact_tmf_ref_model" DROP COLUMN "deletedDate"`,
    );
    await queryRunner.query(
      `ALTER TABLE "artifact_category_versions" DROP COLUMN "deletedDate"`,
    );
    await queryRunner.query(
      `ALTER TABLE "ai_prompt_variable" DROP COLUMN "deletedDate"`,
    );
    await queryRunner.query(
      `ALTER TABLE "artifact_isf_ref_model" DROP COLUMN "deletedDate"`,
    );
  }
}
