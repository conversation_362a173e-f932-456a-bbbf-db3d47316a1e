import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseUUIDPipe,
  UseGuards,
} from '@nestjs/common';
import { AiPromptVariableService } from './services/ai-prompt-variable.service';
import {
  AiPromptVariableUpdateDto,
  CreateAiPromptVariableDto,
} from './dto/create-ai-prompt-variable.dto';
import { UpdateAiPromptVariableDto } from './dto/update-ai-prompt-variable.dto';
import { ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Roles } from 'src/auth/roles/roles.decorator';
import { AiPromptVariable } from 'src/models/entities/ai-prompt-variable.entity';
import { MetadataDto } from 'src/models/dto/meta-data.dto';
import { PaginationDto } from 'src/models/dto/pagination.dto';
import { FilterPromptVariableDto } from 'src/models/dto/filters.dto';
import { JwtAuthGuard } from 'src/auth/jwt-auth.guard';
import { RolesGuard } from 'src/auth/roles/roles.guard';
import { ResolverFunctionsResponse } from './dto/resolver-function-response.dto';

export class PromptVariablesResponse {
  results: AiPromptVariable[];
  metadata: MetadataDto;
}

@ApiTags('AI Prompt Variable')
@Controller({
  path: 'admin/ai-prompt-variable',
  version: '1',
})
@UseGuards(JwtAuthGuard, RolesGuard)
export class AiPromptVariableController {
  constructor(
    private readonly aiPromptVariableService: AiPromptVariableService,
  ) {}

  @Post()
  @Roles('admin')
  @ApiOperation({ summary: 'Create a new AI prompt variable' })
  @ApiResponse({
    status: 201,
    description: 'The AI prompt variable has been successfully created.',
    type: AiPromptVariable,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - validation error',
  })
  @ApiResponse({
    status: 409,
    description: 'Attempt to create a prompt variable with an existed key.',
  })
  create(
    @Body() createAiPromptVariableDto: CreateAiPromptVariableDto,
  ): Promise<AiPromptVariable> {
    return this.aiPromptVariableService.create(createAiPromptVariableDto);
  }

  @Get()
  @Roles('admin')
  @ApiOperation({ summary: 'Get all AI prompt variables' })
  @ApiResponse({
    status: 200,
    description: 'List of all AI prompt variables',
    type: PromptVariablesResponse,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - validation error',
  })
  async getAllPromptVariables(
    @Query() paginationDto: PaginationDto<FilterPromptVariableDto>,
  ): Promise<PromptVariablesResponse> {
    try {
      return await this.aiPromptVariableService.findAll(paginationDto);
    } catch (error) {
      throw error;
    }
  }

  @Get('resolver-functions')
  @Roles('admin')
  @ApiOperation({ summary: 'Get all resolver functions' })
  @ApiResponse({
    status: 200,
    description: 'List of all AI resolver functions',
    type: ResolverFunctionsResponse,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - validation error',
  })
  async getResolverFunctions(): Promise<ResolverFunctionsResponse> {
    try {
      return await this.aiPromptVariableService.getResolverFunctions();
    } catch (error) {
      throw error;
    }
  }

  @Get(':id')
  @Roles('admin')
  @ApiOperation({ summary: 'Get AI prompt variable by ID' })
  @ApiParam({
    name: 'id',
    description: 'UUID of the AI prompt variable',
  })
  @ApiResponse({
    status: 200,
    description: 'The AI prompt variable with the specified ID.',
    type: AiPromptVariable,
  })
  @ApiResponse({
    status: 404,
    description: 'AI prompt variable not found.',
  })
  async getPromptVariableById(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<AiPromptVariable> {
    try {
      return await this.aiPromptVariableService.findOne(id);
    } catch (error) {
      throw error;
    }
  }

  @Patch(':id')
  @Roles('admin')
  @ApiOperation({ summary: 'Update an existing AI prompt variable' })
  @ApiParam({
    name: 'id',
    description: 'UUID of the AI prompt variable',
  })
  @ApiResponse({
    status: 200,
    description: 'The AI prompt has been successfully updated.',
    type: AiPromptVariable,
  })
  @ApiResponse({
    status: 404,
    description: 'AI prompt variable not found.',
  })
  async updatePromptVariable(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updatePromptVariableDto: AiPromptVariableUpdateDto,
  ): Promise<AiPromptVariable> {
    try {
      return this.aiPromptVariableService.update(id, updatePromptVariableDto);
    } catch (error) {
      throw error;
    }
  }

  @Delete(':id')
  @Roles('admin')
  @ApiOperation({ summary: 'Soft-deleting a prompt variable' })
  @ApiParam({
    name: 'id',
    description: 'UUID of the AI prompt variable',
  })
  @ApiResponse({
    status: 200,
    description: 'The AI prompt has been successfully soft-deleted.',
    type: AiPromptVariable,
  })
  @ApiResponse({
    status: 404,
    description: 'AI prompt variable not found.',
  })
  async softDeletePromptVariable(@Param('id', ParseUUIDPipe) id: string) {
    try {
      return this.aiPromptVariableService.softDelete(id);
    } catch (error) {
      throw error;
    }
  }
}
