import { MigrationInterface, QueryRunner } from 'typeorm';

export class ArtifactCategoryVersionAllowNullVersion1754015725303
  implements MigrationInterface
{
  name = 'ArtifactCategoryVersionAllowNullVersion1754015725303';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "artifact_category_versions" ALTER COLUMN "version" DROP NOT NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "artifact_category_versions" ALTER COLUMN "version" SET NOT NULL`,
    );
  }
}
