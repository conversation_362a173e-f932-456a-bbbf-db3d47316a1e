import { Test, TestingModule } from '@nestjs/testing';
import { ArtifactTmfRefModelController } from './artifact-tmf-ref-model.controller';
import { ArtifactTmfRefModelService } from './artifact-tmf-ref-model.service';

describe('ArtifactTmfRefModelController', () => {
  let controller: ArtifactTmfRefModelController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ArtifactTmfRefModelController],
      providers: [ArtifactTmfRefModelService],
    }).compile();

    controller = module.get<ArtifactTmfRefModelController>(
      ArtifactTmfRefModelController,
    );
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
