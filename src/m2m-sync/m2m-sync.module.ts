import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ArtifactCategoryVersions } from '../models/entities/artifact-category-version.entity';
import { AiPromptTemplate } from '../models/entities/ai-prompt-template.entity';
import { AiPromptCache } from '../models/entities/ai-prompt-cache.entity';
import { AiPromptVariable } from '../models/entities/ai-prompt-variable.entity';
import { ArtifactIsfRefModel } from '../models/entities/artifact-isf-ref-model.entity';
import { ArtifactTmfRefModel } from '../models/entities/artifact-tmf-ref-model.entity';
import { SyncService } from './services/m2m-sync.service';
import { SyncController } from './controllers/m2m-sync.controller';
import { ArtifactCategories } from '../models/entities/artifact-category.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      ArtifactCategoryVersions,
      AiPromptTemplate,
      AiPromptCache,
      AiPromptVariable,
      ArtifactIsfRefModel,
      ArtifactTmfRefModel,
      ArtifactCategories,
    ]),
  ],
  controllers: [SyncController],
  providers: [SyncService],
})
export class M2MSyncModule {}
