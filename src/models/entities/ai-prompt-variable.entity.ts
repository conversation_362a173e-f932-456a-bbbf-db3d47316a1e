import { Entity, Column, Index } from 'typeorm';
import { BaseEntity } from '../abstracts/abstract-entity';

export enum PromptVariableType {
  STRING = 'string',
  JSON = 'json',
  ARRAY = 'array',
  NUMBER = 'number',
  BOOLEAN = 'boolean',
}

export enum PromptVariableResolver {
  RESOLVE_PROTOCOL_CONTENT = 'resolveProtocolContent',
}

@Entity({ synchronize: false })
@Index(['lastUpdatedDate'])
@Index(['deletedDate'])
export class AiPromptVariable extends BaseEntity {
  @Column({ type: 'text', unique: true, nullable: false })
  @Index()
  key: string; // e.g., "protocol.content"

  @Column({ type: 'text', nullable: true })
  label: string; // e.g., "Full Protocol Text"

  @Column({ type: 'text', nullable: true })
  description: string; // what this variable contains

  // Retained: This 'type' field is useful and implied by the full spec's JSON example.
  @Column({
    type: 'enum',
    enum: PromptVariableType,
    nullable: false, // Assuming a type is always required. Adjust if optional.
  })
  type: PromptVariableType;

  @Column({ type: 'boolean', default: false })
  computed: boolean; // is this a dynamic or static variable - does it require computing

  // RENAMED from defaultValue to fallbackValue
  @Column({ type: 'jsonb', nullable: true })
  fallbackValue: any; // the value provided in case the resolver function fails

  @Column({ type: 'boolean', default: false })
  published: boolean; // this is set to true when the function has been written in the backend

  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  @Column({
    type: 'enum', // Stored as varchar in DB, but managed as enum in code
    enum: PromptVariableResolver,
    nullable: true, // Only applicable if 'computed' is true
  })
  resolverFunction: PromptVariableResolver | null; // points to the function resolving
}
