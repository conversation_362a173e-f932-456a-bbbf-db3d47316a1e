import { Column, Entity, Index, OneToMany } from 'typeorm';
import { BaseEntity } from '../abstracts/abstract-entity';
import { ArtifactCategories } from './artifact-category.entity';

export enum TMFCore {
  RECOMMENDED = 'recommended',
  CORE = 'core',
}

export enum InvestigatorInitiatedStudyArtifacts {
  MANDATORY = 'mandatory',
  DEPENDENT = 'dependent',
  RECOMMENDED = 'recommended',
}

export enum OriginType {
  TO_ISF = 'to_ISF',
  TO_TMF = 'to_TMF',
}

export enum VersionStatus {
  PUBLISHED = 'published',
  DRAFT = 'draft',
}

@Entity({ synchronize: true })
@Index(['lastUpdatedDate'])
@Index(['deletedDate'])
export class ArtifactCategoryVersions extends BaseEntity {
  @Column({ type: 'int', nullable: true })
  version: number;

  @Column({ type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
  effectiveDate: Date;

  @Column('text', { nullable: true })
  notes: string;

  @Column({
    type: 'enum',
    enum: VersionStatus,
    nullable: false, // Assuming a type is always required. Adjust if optional.
    default: VersionStatus.PUBLISHED,
  })
  status: VersionStatus;

  @OneToMany(() => ArtifactCategories, (category) => category.categoryVersion)
  categories: ArtifactCategories[];
}
