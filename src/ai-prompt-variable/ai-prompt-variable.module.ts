import { Module } from '@nestjs/common';
import { AiPromptVariableService } from './services/ai-prompt-variable.service';
import { AiPromptVariableController } from './ai-prompt-variable.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AiPromptVariable } from 'src/models/entities/ai-prompt-variable.entity';

@Module({
  imports: [TypeOrmModule.forFeature([AiPromptVariable])],
  controllers: [AiPromptVariableController],
  providers: [AiPromptVariableService],
})
export class AiPromptVariableModule {}
