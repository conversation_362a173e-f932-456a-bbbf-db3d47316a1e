name: Development Environment # TBD

on: workflow_dispatch

env:
  SLACK_WEBHOOK_URL: ${{ secrets.ORG_SLACK_WEBHOOK_URL }}

jobs:
  build:
    runs-on: self-hosted
    if: github.event_name == 'workflow_dispatch' && github.ref == 'refs/heads/develop' # TBD
    environment:
      name: development # TBD
    permissions:
      contents: 'read'
      id-token: 'write'
    steps:
      - id: checkout-repo
        name: Checkout repository
        uses: actions/checkout@v4

      - uses: clincove-eng/github-actions/setup-docker@v1
        with:
          service_account: ${{ secrets.ORG_WORKLOAD_IDENTITY_SA_EMAIL }}
          workload_identity_provider: ${{ secrets.ORG_WORKLOAD_IDENTITY_PROVIDER_NAME }}
          registry: ${{ secrets.ORG_ARTIFACT_REGISTRY_BASE_URL }}

      - uses: clincove-eng/github-actions/build-image@v1
        with:
          image_name: ${{ vars.IMAGE_NAME }}
          registry: ${{ vars.REGISTRY }}

  deploy:
    runs-on: self-hosted
    if: github.event_name == 'workflow_dispatch' && github.ref == 'refs/heads/develop' # TBD
    environment:
      name: development # TBD
    needs: build
    permissions:
      contents: 'read'
      id-token: 'write'
    env:
      VERSION: ${{ github.ref_type == 'branch' && github.sha || github.ref_name }}
      IMAGE_REPOSITORY: ${{ vars.REGISTRY }}/${{ vars.IMAGE_NAME }}
      REPLICAS: ${{ vars.REPLICAS }}
      CPU: ${{ vars.CPU }}
      RAM: ${{ vars.RAM }}
      APP_ENV: ${{ vars.APP_ENV }}
      CORS_ORIGINS: ${{ vars.CORS_ORIGINS }}
      DATABASE_LOG: ${{ vars.DATABASE_LOG }}
      DATABASE_HOST: ${{ vars.DATABASE_HOST }}
      DATABASE_USERNAME: ${{ vars.DATABASE_USERNAME }}
      DATABASE_PASSWORD: ${{ secrets.DATABASE_PASSWORD }}
      MAIN_APP_DATABASE_HOST: ${{ vars.MAIN_APP_DATABASE_HOST }}
      MAIN_APP_DATABASE_USERNAME: ${{ vars.MAIN_APP_DATABASE_USERNAME }}
      MAIN_APP_DATABASE_PASSWORD: ${{ secrets.MAIN_APP_DATABASE_PASSWORD }}
      INTERNAL_API_KEY: ${{ secrets.INTERNAL_API_KEY }}
      CLERK_SECRET_KEY: ${{ secrets.CLERK_SECRET_KEY }}
    steps:
      - id: checkout-repo
        name: Checkout repository
        uses: actions/checkout@v4

      - id: auth
        name: Authenticate with gcloud
        uses: google-github-actions/auth@v2
        with:
          service_account: ${{ secrets.ORG_WORKLOAD_IDENTITY_SA_EMAIL }}
          workload_identity_provider: ${{ secrets.ORG_WORKLOAD_IDENTITY_PROVIDER_NAME }}

      - id: replace-template-vars
        name: Replace template variables for helm
        run: envsubst < ci/values.tpl.yml > values.yml

      - uses: clincove-eng/github-actions/deploy-image@v1
        with:
          application_type: ${{ vars.APPLICATION_TYPE }}
          application_name: ${{ vars.APPLICATION_NAME }}
          cluster_name: ${{ vars.US_CLUSTER_NAME }}
          location: ${{ vars.US_CLUSTER_LOCATION }}
          country: ${{ vars.US_COUNTRY }}
