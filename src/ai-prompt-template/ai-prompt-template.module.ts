import { Module } from '@nestjs/common';
import { AiPromptTemplateService } from './ai-prompt-template.service';
import { AiPromptTemplateController } from './ai-prompt-template.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AiPromptTemplate } from 'src/models/entities/ai-prompt-template.entity';

@Module({
  imports: [TypeOrmModule.forFeature([AiPromptTemplate])],
  controllers: [AiPromptTemplateController],
  providers: [AiPromptTemplateService],
})
export class AiPromptTemplateModule {}
