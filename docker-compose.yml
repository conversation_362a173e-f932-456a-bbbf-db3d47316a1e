version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: config-service-db
    environment:
      POSTGRES_DB: config_service
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - config-service-network

  app:
    build:
      context: .
      dockerfile: docker/Dockerfile
    container_name: config-service-app
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - DATABASE_HOST=postgres
      - DATABASE_PORT=5432
      - DATABASE_USERNAME=postgres
      - DATABASE_PASSWORD=postgres
      - DATABASE_NAME=config_service
      - DATABASE_LOG=false
      - CORS_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:8080
    depends_on:
      - postgres
    networks:
      - config-service-network
    restart: unless-stopped

  migration:
    build:
      context: .
      dockerfile: docker/Dockerfile.migration
    container_name: config-service-migration
    environment:
      - DATABASE_HOST=postgres
      - DATABASE_PORT=5432
      - DATABASE_USERNAME=postgres
      - DATABASE_PASSWORD=postgres
      - DATABASE_NAME=config_service
      - DATABASE_LOG=false
    depends_on:
      - postgres
    networks:
      - config-service-network

volumes:
  postgres_data:

networks:
  config-service-network:
    driver: bridge 