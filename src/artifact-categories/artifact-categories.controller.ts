import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Put,
  ParseUUIDPipe,
  Query,
  Res,
  UseInterceptors,
  UploadedFile,
  BadRequestException,
} from '@nestjs/common';
import { ArtifactCategoriesService } from './artifact-categories.service';
import {
  CategoryVersionInfoDto,
  CreateArtifactCategoryDto,
} from './dto/create-artifact-category.dto';
import { UpdateArtifactCategoryDto } from './dto/update-artifact-category.dto';
import {
  ApiBody,
  ApiConsumes,
  ApiOperation,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { JwtAuthGuard } from 'src/auth/jwt-auth.guard';
import { RolesGuard } from 'src/auth/roles/roles.guard';
import { InjectPinoLogger, PinoLogger } from 'nestjs-pino';
import { Roles } from 'src/auth/roles/roles.decorator';
import { ArtifactCategoryVersions } from 'src/models/entities/artifact-category-version.entity';
import { PaginationDto } from 'src/models/dto/pagination.dto';
import {
  FilterArtifactCategoriesDto,
  FilterArtifactCategoryVersionDto,
} from 'src/models/dto/filters.dto';
import { Response } from 'express';
import { FileInterceptor } from '@nestjs/platform-express';
import { ImportCategoriesResultDto } from './dto/import-response.dto';
import { Multer } from 'multer';
import { UpdateCategoryVersionDto } from './dto/artifact-category-version.dto';

@ApiTags('Artifact Categories')
@Controller({
  path: ['admin/artifact-categories'],
  version: '1',
})
@UseGuards(JwtAuthGuard, RolesGuard)
export class ArtifactCategoriesController {
  constructor(
    private readonly artifactCategoriesService: ArtifactCategoriesService,
    @InjectPinoLogger(ArtifactCategoriesController.name)
    protected readonly logger: PinoLogger,
  ) {}

  @Post()
  @Roles('admin')
  @ApiOperation({ summary: 'Create a new artifact category' })
  async createCategory(@Body() createDto: CreateArtifactCategoryDto) {
    try {
      return await this.artifactCategoriesService.createCategory(createDto);
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }
  @Post('import')
  @Roles('admin')
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary:
      'Import artifact categories from a CSV file to create a new version.',
  })
  @ApiBody({
    description:
      'A CSV file formatted like the export, containing artifact categories.',
    required: true,
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Successfully imported categories and created a new version.',
    type: ImportCategoriesResultDto,
  })
  @ApiResponse({
    status: 400,
    description:
      'Bad Request: Invalid file, CSV parsing error, or data validation failure.',
  })
  @ApiResponse({
    status: 409,
    description:
      'Conflict: A version conflict occurred (e.g., trying to import a version that already exists).',
  })
  async importArtifactCategories(@UploadedFile() file: Multer.File) {
    this.logger.info(
      'Received request to import artifact categories from CSV.',
    );

    if (!file) {
      throw new BadRequestException(
        'No file was uploaded. Please provide a CSV file.',
      );
    }

    // Delegate the entire complex logic of parsing, validation, and transactional saving to the service.
    return this.artifactCategoriesService.importCategoriesFromCsv(file.buffer);
  }

  @Get()
  @Roles('admin')
  @ApiOperation({
    summary: 'Get all artifact categories with pagination and filtering',
  })
  async getAllArtifactCategories(
    @Query() paginationDto: PaginationDto<FilterArtifactCategoriesDto>,
  ) {
    try {
      return this.artifactCategoriesService.getAllArtifactCategories(
        paginationDto,
      );
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  @Get('versions')
  @Roles('admin')
  @ApiOperation({ summary: 'Get all category versions' })
  async getCategoryVersions(
    @Query() paginationDto: PaginationDto<FilterArtifactCategoryVersionDto>,
  ) {
    try {
      return await this.artifactCategoriesService.getCategoryVersions(
        paginationDto,
      );
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  @Get('versions/latest')
  @Roles('admin')
  @ApiOperation({ summary: 'Get the latest category version' })
  async getLatestCategoryVersion() {
    try {
      return await this.artifactCategoriesService.getLatestCategoryVersion();
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  @Get('versions/:id')
  @Roles('admin')
  @ApiOperation({ summary: 'Get a category version' })
  async getCategoryVersion(@Param('id', ParseUUIDPipe) id: string) {
    try {
      return await this.artifactCategoriesService.getCategoryVersionById(id);
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  @Get('export')
  @Roles('admin')
  @ApiOperation({
    summary: 'Export artifact categories as a CSV file.',
    description:
      'Exports a specific version if a version number is provided, otherwise defaults to the latest version.',
  })
  @ApiQuery({
    name: 'version',
    required: false,
    description:
      'The specific version number to export. If omitted, the latest version will be exported.',
    type: Number,
  })
  @ApiResponse({
    status: 200,
    description: 'A CSV file of the requested artifact categories.',
  })
  @ApiResponse({
    status: 404,
    description: 'The requested version does not exist.',
  })
  async exportArtifactCategories(
    @Res({ passthrough: true }) res: Response,
    @Query('version') version?: number,
  ) {
    try {
      const logMessage = version
        ? `Received request to export artifact categories for version ${version}.`
        : 'Received request to export latest artifact categories.';
      this.logger.info(logMessage);
      const { csvData, versionNumber } =
        await this.artifactCategoriesService.exportCategoriesAsCsv(version);

      const date = new Date().toISOString().split('T')[0];
      const filename = `${date}_ISF-TMF-ref-export_V${versionNumber}.csv`;

      res.setHeader('Content-Type', 'text/csv');
      res.setHeader(
        'Content-Disposition',
        `attachment; filename="${filename}"`,
      );

      return csvData;
    } catch (error) {
      this.logger.error(error, 'Failed to export artifact categories.');
      throw error;
    }
  }

  @Get(':id')
  @Roles('admin')
  @ApiOperation({
    summary: 'Get a category by ID',
  })
  async getCategoryById(@Param('id', ParseUUIDPipe) id: string) {
    return this.artifactCategoriesService.getCategoryById(id);
  }

  @Put(':id')
  @Roles('admin')
  @ApiOperation({ summary: 'Update an artifact category' })
  async updateCategory(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateDto: UpdateArtifactCategoryDto,
  ) {
    try {
      return await this.artifactCategoriesService.updateCategory(id, updateDto);
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  @Patch(':id/archive')
  @Roles('admin')
  @ApiOperation({ summary: 'Archive an artifact category' })
  async archiveCategory(@Param('id', ParseUUIDPipe) id: string) {
    try {
      return await this.artifactCategoriesService.archiveCategory(id, true);
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  @Patch(':id/restore')
  @Roles('admin')
  @ApiOperation({ summary: 'Restore an archived artifact category' })
  async restoreCategory(@Param('id', ParseUUIDPipe) id: string) {
    try {
      return await this.artifactCategoriesService.archiveCategory(id, false);
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  @Patch('versions/:versionId/finalize')
  @Roles('admin')
  @ApiOperation({ summary: 'Finalize a draft version' })
  async finalize(@Param('versionId', ParseUUIDPipe) versionId: string) {
    try {
      return await this.artifactCategoriesService.finalizeVersion(versionId);
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  @Patch('versions/:id')
  @Roles('admin')
  @ApiOperation({ summary: 'Update a category version' })
  async updateCategoryVersion(
    @Param('id') id: string,
    @Body() updateDto: UpdateCategoryVersionDto,
  ) {
    try {
      return await this.artifactCategoriesService.updateCategoryVersion(
        id,
        updateDto,
      );
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  @Post('versions')
  @Roles('admin')
  @ApiOperation({ summary: 'Create a new category version' })
  async createCategoryVersion(@Body() createDto: CategoryVersionInfoDto) {
    try {
      return await this.artifactCategoriesService.createCategoryVersion(
        createDto,
      );
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  @Delete(':id')
  @Roles('admin')
  @ApiOperation({ summary: 'Create a new category version' })
  async softDeleteCategory(@Param('id', ParseUUIDPipe) id: string) {
    return this.artifactCategoriesService.softDeleteCategory(id);
  }

  @Delete('versions/:versionId')
  @Roles('admin')
  @ApiResponse({
    status: 200,
    description: 'Delete an artifact category version',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - validation error',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description:
      'Attempt to delete an artifact category version that links to an artifact category.',
  })
  async deleteVersion(
    @Param('versionId', ParseUUIDPipe) versionId: string,
  ): Promise<ArtifactCategoryVersions> {
    try {
      return await this.artifactCategoriesService.deleteVersion(versionId);
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }
}
