import {
  BadRequestException,
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import {
  CategoryVersionInfoDto,
  CreateArtifactCategoryDto,
} from './dto/create-artifact-category.dto';
import { UpdateArtifactCategoryDto } from './dto/update-artifact-category.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { ArtifactCategories } from 'src/models/entities/artifact-category.entity';
import { DataSource, ILike, In, Repository } from 'typeorm';
import {
  ArtifactCategoryVersions,
  InvestigatorInitiatedStudyArtifacts,
  OriginType,
  TMFCore,
  VersionStatus,
} from 'src/models/entities/artifact-category-version.entity';
import { InjectPinoLogger, PinoLogger } from 'nestjs-pino';
import { ArtifactTmfRefModel } from 'src/models/entities/artifact-tmf-ref-model.entity';
import { ArtifactIsfRefModel } from 'src/models/entities/artifact-isf-ref-model.entity';
import { PaginationDto } from 'src/models/dto/pagination.dto';
import {
  FilterArtifactCategoriesDto,
  FilterArtifactCategoryVersionDto,
} from 'src/models/dto/filters.dto';
import { MetadataDto } from 'src/models/dto/meta-data.dto';
import * as Papa from 'papaparse';
import { ExportResult } from './dto/export-result.dto';
import { ImportCategoriesResultDto } from './dto/import-response.dto';
import { CategoryCsvRow } from './dto/category-csv-row.dto';
import { UpdateCategoryVersionDto } from './dto/artifact-category-version.dto';

@Injectable()
export class ArtifactCategoriesService {
  constructor(
    @InjectRepository(ArtifactCategories)
    private categoriesRepository: Repository<ArtifactCategories>,
    @InjectRepository(ArtifactCategoryVersions)
    private categoryVersionsRepository: Repository<ArtifactCategoryVersions>,
    @InjectPinoLogger(ArtifactCategoriesService.name)
    private readonly logger: PinoLogger,
    @InjectRepository(ArtifactTmfRefModel)
    private tmfRefModelRepository: Repository<ArtifactTmfRefModel>,
    @InjectRepository(ArtifactIsfRefModel)
    private isfRefModelRepository: Repository<ArtifactIsfRefModel>,
    private dataSource: DataSource,
  ) {}

  async updateCategoryVersion(id: string, updateDto: UpdateCategoryVersionDto) {
    try {
      const categoryVersion = await this.categoryVersionsRepository.findOne({
        where: { id },
      });

      if (!categoryVersion) {
        throw new NotFoundException(`Category version with ID ${id} not found`);
      }

      Object.assign(categoryVersion, updateDto);

      return await this.categoryVersionsRepository.save(categoryVersion);
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async createCategory(
    createDto: CreateArtifactCategoryDto,
  ): Promise<ArtifactCategories> {
    try {
      // Validation using renamed DTO fields
      if (!createDto.isTMF && !createDto.isISF) {
        throw new BadRequestException(
          'An artifact category must belong to at least one framework (isTMF or isISF must be true).',
        );
      }

      const categoryVersion = await this.categoryVersionsRepository.findOne({
        where: { id: createDto.categoryVersionId },
      });
      if (!categoryVersion) {
        throw new NotFoundException(
          `Category version with ID ${createDto.categoryVersionId} not found`,
        );
      }

      // Conditionally validate TMF Ref Model only if it's a TMF artifact
      if (createDto.isTMF) {
        const tmfRefModel = await this.tmfRefModelRepository.findOne({
          where: { id: createDto.tmfRefModelId },
        });
        if (!tmfRefModel) {
          throw new NotFoundException(
            `TMF Ref Model with ID ${createDto.tmfRefModelId} not found.`,
          );
        }
      }

      // Conditionally validate ISF Ref Model only if it's an ISF artifact
      if (createDto.isISF) {
        const isfRefModel = await this.isfRefModelRepository.findOne({
          where: { id: createDto.isfRefModelId },
        });
        if (!isfRefModel) {
          throw new NotFoundException(
            `ISF Ref Model with ID ${createDto.isfRefModelId} not found.`,
          );
        }
      }

      const newCategory = this.categoriesRepository.create({
        ...createDto,
        isActive: createDto.isActive !== undefined ? createDto.isActive : true,
      });

      const savedCategory = await this.categoriesRepository.save(newCategory);

      return this.categoriesRepository.findOne({
        where: { id: savedCategory.id },
        relations: ['tmfRefModel', 'isfRefModel', 'categoryVersion'],
      });
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async getAllArtifactCategories(
    paginationDto: PaginationDto<FilterArtifactCategoriesDto>,
  ) {
    const take = paginationDto.take || 500;
    const filter = paginationDto.filter ?? {};
    const orders = filter?.orders ?? [];
    const useOrderList = Boolean(orders.length);
    const validOrderByFields = [
      'tmfZoneName',
      'tmfSectionName',
      'tmfRecordGroupName',
      'isfZoneName',
      'isfSectionName',
      'isfRecordGroupName',
      'recordType',
      'origin',
      'iitStudyArtifacts',
      'tmfZoneNumber',
      'tmfSectionNumber',
      'tmfRecordGroupNumber',
      'isfZoneNumber',
      'isfSectionNumber',
      'isfRecordGroupNumber',
      'tmfCore',
      'alternativeNames',
    ];

    const orderByJoinedFields = ['tmfRefModel', 'isfRefModel'];

    const defaultOrderBy = 'recordType';
    const orderBy = paginationDto.orderBy || defaultOrderBy;
    const orderDirection =
      paginationDto.orderDirection || ('ASC' as 'ASC' | 'DESC');

    if (
      useOrderList
        ? orders
            ?.map((x) => x.orderBy)
            .some(
              (k) =>
                !validOrderByFields.includes(k) &&
                !orderByJoinedFields.includes(k),
            )
        : !validOrderByFields.includes(orderBy) &&
          !orderByJoinedFields.includes(orderBy)
    ) {
      this.logger.info(`${orderBy} does not exist in paginationDto.filter`);
      throw new BadRequestException(
        `${orderBy} does not exist in paginationDto.filter`,
      );
    }

    if (filter?.versionLatest === 'true' || filter?.version) {
      const versionQb = this.categoryVersionsRepository
        .createQueryBuilder('categoryVersion')
        .orderBy('categoryVersion.version', 'DESC'); // Assuming createdDate exists in ArtifactCategoryVersions
      if (filter?.version) {
        versionQb.andWhere('categoryVersion.version = :version', {
          version: filter.version,
        });
      }
      const latestVersion = await versionQb.getOne();
      if (latestVersion) {
        filter.categoryVersionId = latestVersion.id;
      }
    }

    try {
      const toListOrder = () => {
        orders.forEach((order) => {
          order.orderBy = toOrderKey(order.orderBy);
        });

        return orders;
      };

      const toOrderKey = (key: string) => {
        if (orderByJoinedFields.includes(key)) {
          switch (key) {
            case 'tmfRefModel':
              return 'tmfRefModel.tmfRefModel';
            case 'isfRefModel':
              return 'isfRefModel.isfRefModel';
          }
        }
        return `categories.${key}`;
      };

      const qb = this.categoriesRepository
        .createQueryBuilder('categories')
        .leftJoinAndSelect('categories.categoryVersion', 'categoryVersion')
        .leftJoinAndSelect('categories.tmfRefModel', 'tmfRefModel')
        .leftJoinAndSelect('categories.isfRefModel', 'isfRefModel');

      if (filter?.isActive !== undefined) {
        qb.andWhere({
          isActive: filter.isActive,
        });
      }

      if (filter?.tmfZoneName) {
        qb.andWhere({
          tmfZoneName: ILike(`%${filter.tmfZoneName}%`),
        });
      }

      if (filter?.isfZoneName) {
        qb.andWhere({
          isfZoneName: ILike(`%${filter.isfZoneName}%`),
        });
      }

      if (filter?.tmfSectionName) {
        qb.andWhere({
          tmfSectionName: ILike(`%${filter.tmfSectionName}%`),
        });
      }

      if (filter?.isfSectionName) {
        qb.andWhere({
          isfSectionName: ILike(`%${filter.isfSectionName}%`),
        });
      }

      if (filter?.tmfRecordGroupName) {
        qb.andWhere({
          tmfRecordGroupName: ILike(`%${filter.tmfRecordGroupName}%`),
        });
      }

      if (filter?.isfRecordGroupName) {
        qb.andWhere({
          isfRecordGroupName: ILike(`%${filter.isfRecordGroupName}%`),
        });
      }

      if (filter?.recordType) {
        qb.andWhere({
          recordType: ILike(`%${filter.recordType}%`),
        });
      }

      if (filter?.alternativeNames) {
        qb.andWhere({
          alternativeNames: ILike(`%${filter.alternativeNames}%`),
        });
      }

      if (filter?.categoryVersionId) {
        qb.andWhere({
          categoryVersionId: filter.categoryVersionId,
        });
      }
      if (filter?.isTMF !== undefined) {
        qb.andWhere({ isTMF: filter.isTMF });
      }

      if (filter?.isISF !== undefined) {
        qb.andWhere({ isISF: filter.isISF });
      }

      if (filter?.origin) {
        qb.andWhere({ origin: filter.origin });
      }

      if (filter?.iitStudyArtifacts) {
        qb.andWhere({
          iitStudyArtifacts: filter.iitStudyArtifacts,
        });
      }

      if (
        filter?.requiresSignature !== null &&
        filter?.requiresSignature !== undefined
      ) {
        qb.andWhere({
          requiresSignature: filter.requiresSignature,
        });
      }

      if (filter?.expires !== null && filter?.expires !== undefined) {
        qb.andWhere({
          expires: filter.expires,
        });
      }

      if (
        filter?.inspectableRecord !== null &&
        filter?.inspectableRecord !== undefined
      ) {
        qb.andWhere({
          inspectableRecord: filter.inspectableRecord,
        });
      }

      if (filter?.includesPHI !== null && filter?.includesPHI !== undefined) {
        qb.andWhere({
          includesPHI: filter.includesPHI,
        });
      }

      if (useOrderList) {
        toListOrder().forEach((i) => {
          qb.addOrderBy(i.orderBy, i.orderDirection);
        });
      } else {
        qb.addOrderBy(
          toOrderKey(orderBy),
          orderDirection.toUpperCase() as 'DESC' | 'ASC',
        );
      }

      const [results, count] = await qb
        .skip(paginationDto.skip)
        .take(take)
        .getManyAndCount();

      const metadata: MetadataDto = {
        totalCount: count,
        itemCount: results.length,
        itemsPerPage: take,
        totalPages: Math.ceil(count / take),
        currentPage: paginationDto.page,
      };

      return { results, metadata };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async getCategoryVersions(
    paginationDto: PaginationDto<FilterArtifactCategoryVersionDto>,
  ) {
    const take = paginationDto.take || 500;
    const validOrderByFields = ['effectiveDate', 'id', 'version'];
    const defaultOrderBy = 'id';
    const orderBy = paginationDto.orderBy || defaultOrderBy;
    const orderDirection =
      paginationDto.orderDirection || ('ASC' as 'ASC' | 'DESC');

    if (!validOrderByFields.includes(orderBy)) {
      this.logger.info(`${orderBy} does not exist in paginationDto.filter`);
      throw new BadRequestException(
        `${orderBy} does not exist in paginationDto.filter`,
      );
    }

    try {
      const options = {
        take: take,
        skip: (paginationDto.page - 1) * paginationDto.take,
        where: {
          ...(paginationDto.filter?.version && {
            version: paginationDto.filter.version,
          }),
        },
        order: { [orderBy]: orderDirection },
      };

      const [results, count] =
        await this.categoryVersionsRepository.findAndCount(options);

      if (results.length === 0) {
        return {
          results: [],
          metadata: {
            totalCount: 0,
            itemCount: 0,
            itemsPerPage: take,
            totalPages: 0,
            currentPage: paginationDto.page,
          },
        };
      }

      const metadata: MetadataDto = {
        totalCount: count,
        itemCount: results.length,
        itemsPerPage: take,
        totalPages: Math.ceil(count / paginationDto.take),
        currentPage: paginationDto.page,
      };

      return { results, metadata };
    } catch (error) {
      this.logger.error('Failed to get category versions', error.stack);
      throw error;
    }
  }

  async getLatestCategoryVersion() {
    try {
      return await this.categoryVersionsRepository.findOne({
        where: {},
        order: { version: 'DESC' },
      });
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async getCategoryVersionById(id: string) {
    try {
      const categoryVersion = await this.categoryVersionsRepository.findOne({
        where: { id },
        relations: ['categories'],
      });
      if (!categoryVersion) {
        throw new NotFoundException(
          `CategoryVersion with ID "${id}" not found.`,
        );
      }

      return categoryVersion;
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async getCategoryById(id: string): Promise<ArtifactCategories> {
    try {
      const category = await this.categoriesRepository.findOne({
        where: { id },
        relations: ['tmfRefModel', 'isfRefModel', 'categoryVersion'],
      });

      if (!category) {
        throw new NotFoundException(`Category with ID ${id} not found`);
      }

      return category;
    } catch (error) {
      throw error;
    }
  }

  async updateCategory(
    id: string,
    updateDto: UpdateArtifactCategoryDto,
  ): Promise<ArtifactCategories> {
    try {
      // 1. Fetch the existing entity from the database.
      const category = await this.categoriesRepository.findOne({
        where: { id },
      });
      if (!category) {
        throw new NotFoundException(
          `Artifact category with ID ${id} not found.`,
        );
      }

      // 2. Create a "merged" object to represent the final state for validation purposes.
      // This combines the existing data with the incoming changes.
      const mergedCategory = { ...category, ...updateDto };

      // 3. Perform business logic validation on the MERGED state.

      // Rule A: Ensure at least one framework is still active.
      if (!mergedCategory.isTMF && !mergedCategory.isISF) {
        throw new BadRequestException(
          'An artifact category must belong to at least one framework (isTMF or isISF must be true).',
        );
      }

      // Rule B: If the final state is TMF, ensure all TMF fields have values.
      if (mergedCategory.isTMF) {
        if (
          !mergedCategory.tmfRefModelId ||
          !mergedCategory.tmfZoneNumber ||
          !mergedCategory.tmfZoneName ||
          !mergedCategory.tmfSectionNumber ||
          !mergedCategory.tmfSectionName ||
          !mergedCategory.tmfRecordGroupNumber ||
          !mergedCategory.tmfRecordGroupName
        ) {
          throw new BadRequestException(
            'When isTMF is true, all tmf* fields and tmfRefModelId are required.',
          );
        }
      }

      // Rule C: If the final state is ISF, ensure all ISF fields have values.
      if (mergedCategory.isISF) {
        if (
          !mergedCategory.isfRefModelId ||
          !mergedCategory.isfZoneNumber ||
          !mergedCategory.isfZoneName ||
          !mergedCategory.isfSectionNumber ||
          !mergedCategory.isfSectionName ||
          !mergedCategory.isfRecordGroupNumber ||
          !mergedCategory.isfRecordGroupName
        ) {
          throw new BadRequestException(
            'When isISF is true, all isf* fields and isfRefModelId are required.',
          );
        }
      }

      // 4. Validate existence of any NEWLY provided related entities.
      if (
        updateDto.categoryVersionId &&
        updateDto.categoryVersionId !== category.categoryVersionId
      ) {
        const versionExists = await this.categoryVersionsRepository.findOne({
          where: { id: updateDto.categoryVersionId },
        });
        if (!versionExists) {
          throw new NotFoundException(
            `Category Version with ID ${updateDto.categoryVersionId} not found.`,
          );
        }
      }
      if (
        updateDto.tmfRefModelId &&
        updateDto.tmfRefModelId !== category.tmfRefModelId
      ) {
        const tmfModelExists = await this.tmfRefModelRepository.findOne({
          where: { id: updateDto.tmfRefModelId },
        });
        if (!tmfModelExists) {
          throw new NotFoundException(
            `TMF Ref Model with ID ${updateDto.tmfRefModelId} not found.`,
          );
        }
      }
      if (
        updateDto.isfRefModelId &&
        updateDto.isfRefModelId !== category.isfRefModelId
      ) {
        const isfModelExists = await this.isfRefModelRepository.findOne({
          where: { id: updateDto.isfRefModelId },
        });
        if (!isfModelExists) {
          throw new NotFoundException(
            `ISF Ref Model with ID ${updateDto.isfRefModelId} not found.`,
          );
        }
      }

      // 5. Apply the updates to the entity.
      Object.assign(category, updateDto);

      // 6. Save the updated entity and return it with relations.
      const updatedCategory = await this.categoriesRepository.save(category);
      return this.categoriesRepository.findOne({
        where: { id: updatedCategory.id },
        relations: ['tmfRefModel', 'isfRefModel', 'categoryVersion'],
      });
    } catch (error) {
      if (
        error instanceof BadRequestException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      this.logger.error(
        `Failed to update artifact category ${id}: ${error.message}`,
        error.stack,
      );
      throw new Error(
        `An internal error occurred while updating the category.`,
      );
    }
  }

  async archiveCategory(id: string, archive: boolean) {
    try {
      const category = await this.categoriesRepository.findOne({
        where: { id },
      });

      if (!category) {
        throw new NotFoundException(
          `Artifact category with ID ${id} not found`,
        );
      }

      category.isActive = !archive;

      return await this.categoriesRepository.save(category);
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async finalizeVersion(id: string): Promise<ArtifactCategoryVersions> {
    try {
      const version = await this.categoryVersionsRepository.findOne({
        where: { id },
      });
      if (!version) {
        throw new NotFoundException('Version not found.');
      }
      version.status = VersionStatus.PUBLISHED;
      return await this.categoryVersionsRepository.save(version);
    } catch (error) {
      throw error;
    }
  }

  async createCategoryVersion(createDto: CategoryVersionInfoDto) {
    try {
      const newVersion = this.categoryVersionsRepository.create({
        ...createDto,
        effectiveDate: createDto?.effectiveDate || new Date(),
        status: VersionStatus.DRAFT,
      });

      return await this.categoryVersionsRepository.save(newVersion);
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }
  async softDeleteCategory(id: string) {
    try {
      const category = await this.categoriesRepository.findOne({
        where: { id: id },
      });
      if (!category) {
        throw new NotFoundException('Category not found');
      }
      return await this.categoriesRepository.softRemove(category);
    } catch (error) {
      throw error;
    }
  }

  async deleteVersion(versionId: string): Promise<ArtifactCategoryVersions> {
    try {
      const linkedCategoryCount = await this.categoriesRepository.count({
        where: { categoryVersionId: versionId },
      });

      if (linkedCategoryCount > 0) {
        throw new ConflictException(
          'Cannot delete versions that link to an Artifact Category',
        );
      }

      const categoryVersion = await this.categoryVersionsRepository.findOne({
        where: { id: versionId },
      });
      if (!categoryVersion) {
        throw new NotFoundException('Category version not found');
      }

      return await this.categoryVersionsRepository.softRemove(categoryVersion);
    } catch (error) {
      if (
        !(
          error instanceof ConflictException ||
          error instanceof NotFoundException ||
          error instanceof BadRequestException
        )
      ) {
        this.logger.error(
          `Failed to remove version ${versionId}: ${error.message}`,
          error.stack,
        );
      }
      throw error;
    }
  }

  async importCategoriesFromCsv(
    fileBuffer: Buffer,
  ): Promise<ImportCategoriesResultDto> {
    // --- CSV HEADER VALIDATION ---
    const requiredFields = [
      'tmf_zone_num',
      'tmf_zone_name',
      'tmf_section_num',
      'tmf_section_name',
      'tmf_record_group_num',
      'tmf_record_group_name',
      'record_type',
      'alternative_names',
      'is_tmf',
      'is_isf',
      'isf_zone_num',
      'isf_zone_name',
      'isf_section_num',
      'isf_section_name',
      'isf_record_group_num',
      'isf_record_group_name',
      'description',
      'requires_signature',
      'expires',
      'inspectable_record',
      'core_or_recommended',
      'includes_phi',
      'naming_convention',
      'origin',
      'iit_study_artifact',
      'tmf_ref_model',
      'isf_ref_model',
      'category_version',
    ];

    const fileContent = fileBuffer.toString('utf-8');
    const { meta } = Papa.parse(fileContent, {
      header: true,
      skipEmptyLines: 'greedy',
      transformHeader: (header) =>
        header.trim().toLowerCase().replace(/ /g, '_'),
      preview: 1,
    });
    const actualFields = meta.fields || [];
    const missingFields = requiredFields.filter(
      (f) => !actualFields.includes(f),
    );
    const extraFields = actualFields.filter((f) => !requiredFields.includes(f));
    if (missingFields.length > 0 || extraFields.length > 0) {
      throw new BadRequestException(
        'Upload unsuccessful. CSV file is not correctly formatted . Please check the file and try again',
      );
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const { data: rows, errors: parseErrors } = Papa.parse<CategoryCsvRow>(
        fileContent,
        {
          header: true,
          skipEmptyLines: 'greedy',
          transformHeader: (header) =>
            header.trim().toLowerCase().replace(/ /g, '_'),
        },
      );

      if (parseErrors.length > 0) {
        throw new BadRequestException(
          `CSV parsing error on row ${parseErrors[0].row}: ${parseErrors[0].message}`,
        );
      }
      if (rows.length === 0) {
        throw new BadRequestException('CSV file contains no data rows.');
      }

      // --- RESTORED & IMPROVED: Validate consistency across all rows ---
      const uniqueVersions = new Set(
        rows.map((row) => row.category_version).filter(Boolean),
      );
      const uniqueTmfModels = new Set(
        rows.map((row) => row.tmf_ref_model).filter(Boolean),
      );
      const uniqueIsfModels = new Set(
        rows.map((row) => row.isf_ref_model).filter(Boolean),
      );

      if (uniqueVersions.size > 1) {
        throw new BadRequestException(
          'CSV validation failed: All rows in the CSV must have the same "category_version".',
        );
      }
      if (uniqueTmfModels.size > 1) {
        throw new BadRequestException(
          'CSV validation failed: All rows in the CSV must have the same "tmf_ref_model".',
        );
      }
      if (uniqueIsfModels.size > 1) {
        throw new BadRequestException(
          'CSV validation failed: All rows in the CSV must have the same "isf_ref_model".',
        );
      }

      const categoryVersionsRepo = queryRunner.manager.getRepository(
        ArtifactCategoryVersions,
      );
      const versionFromCsv = uniqueVersions.values().next().value
        ? parseInt(uniqueVersions.values().next().value, 10)
        : undefined;

      const latestVersionInDb = await categoryVersionsRepo.findOne({
        where: {},
        order: { version: 'DESC' },
      });
      const nextVersionNumber = latestVersionInDb
        ? latestVersionInDb.version + 1
        : 1;
      let targetVersion: ArtifactCategoryVersions;
      let importMessage: string;
      let existingVersion: ArtifactCategoryVersions | null;
      if (versionFromCsv) {
        existingVersion = await categoryVersionsRepo.findOne({
          where: { version: versionFromCsv },
        });
        if (existingVersion) {
          targetVersion = existingVersion;
          importMessage = `Import successful. Categories added to existing version ${targetVersion.version}.`;
        } else {
          this.logger.warn(
            `Version ${versionFromCsv} from CSV not found. Creating new version ${nextVersionNumber} instead.`,
          );
          targetVersion = categoryVersionsRepo.create({
            version: nextVersionNumber,
          });
          await categoryVersionsRepo.save(targetVersion);
          importMessage = `Import successful. New version ${targetVersion.version} created.`;
        }
      } else {
        this.logger.info(
          `No version specified in CSV. Creating new version ${nextVersionNumber}.`,
        );
        targetVersion = categoryVersionsRepo.create({
          version: nextVersionNumber,
        });
        await categoryVersionsRepo.save(targetVersion);
        importMessage = `Import successful. New version ${targetVersion.version} created.`;
      }
      const [validTmfModels, validIsfModels] = await Promise.all([
        this.tmfRefModelRepository.findBy({
          tmfRefModel: In([...uniqueTmfModels]),
        }),
        this.isfRefModelRepository.findBy({
          isfRefModel: In([...uniqueIsfModels]),
        }),
      ]);

      const tmfMap = new Map(validTmfModels.map((m) => [m.tmfRefModel, m.id]));
      const isfMap = new Map(validIsfModels.map((m) => [m.isfRefModel, m.id]));

      const categoriesToCreate: Partial<ArtifactCategories>[] = [];
      const validationErrors: string[] = [];

      for (const [index, row] of rows.entries()) {
        const rowIndex = index + 2; // CSV row number for error messages

        // BUG FIX: Track errors for this specific row
        const initialErrorCount = validationErrors.length;

        const isTMF = this.parseBooleanFromCsv(row.is_tmf, 'X') || false;
        const isISF = this.parseBooleanFromCsv(row.is_isf, 'X') || false;

        // Core validation
        if (!isTMF && !isISF) {
          validationErrors.push(
            `Row ${rowIndex}: At least one of 'is_tmf' or 'is_isf' must be 'X'.`,
          );
        }
        if (!row.record_type) {
          validationErrors.push(`Column "record_type" should not be empty.`);
        }
        if (!row.tmf_ref_model || row.tmf_ref_model === '') {
          throw new BadRequestException(
            'Column "tmf_ref_model" should not be empty.',
          );
        }
        if (!row.isf_ref_model || row.isf_ref_model === '') {
          throw new BadRequestException(
            'Column "isf_ref_model" should not be empty.',
          );
        }

        // Check for not-null constraint
        if (!tmfMap.has(row.tmf_ref_model)) {
          validationErrors.push(
            row.tmf_ref_model
              ? 'Column "tmf_ref_model" is invalid.'
              : 'Column "tmf_ref_model" should not be empty.',
          );
        }

        if (!isfMap.has(row.isf_ref_model)) {
          validationErrors.push(
            row.isf_ref_model
              ? 'Column "isf_ref_model" is invalid.'
              : 'Column "isf_ref_model" should not be empty.',
          );
        }

        // TMF-specific validation
        if (isTMF) {
          if (!row.tmf_zone_num)
            validationErrors.push(
              `Row ${rowIndex}: 'tmf_zone_num' is required when is_tmf is 'X'.`,
            );
          if (!row.tmf_zone_name)
            validationErrors.push(
              `Row ${rowIndex}: 'tmf_zone_name' is required when is_tmf is 'X'.`,
            );
          if (!row.tmf_section_num)
            validationErrors.push(
              `Row ${rowIndex}: 'tmf_section_num' is required when is_tmf is 'X'.`,
            );
          if (!row.tmf_section_name)
            validationErrors.push(
              `Row ${rowIndex}: 'tmf_section_name' is required when is_tmf is 'X'.`,
            );
          if (!row.tmf_record_group_num)
            validationErrors.push(
              `Row ${rowIndex}: 'tmf_record_group_num' is required when is_tmf is 'X'.`,
            );
          if (!row.tmf_record_group_name)
            validationErrors.push(
              `Row ${rowIndex}: 'tmf_record_group_name' is required when is_tmf is 'X'.`,
            );
        }

        // ISF-specific validation
        if (isISF) {
          if (!row.isf_zone_num)
            validationErrors.push(
              `Row ${rowIndex}: 'isf_zone_num' is required when is_isf is 'X'.`,
            );
          if (!row.isf_zone_name)
            validationErrors.push(
              `Row ${rowIndex}: 'isf_zone_name' is required when is_isf is 'X'.`,
            );
          if (!row.isf_section_num)
            validationErrors.push(
              `Row ${rowIndex}: 'isf_section_num' is required when is_isf is 'X'.`,
            );
          if (!row.isf_section_name)
            validationErrors.push(
              `Row ${rowIndex}: 'isf_section_name' is required when is_isf is 'X'.`,
            );
          if (!row.isf_record_group_num)
            validationErrors.push(
              `Row ${rowIndex}: 'isf_record_group_num' is required when is_isf is 'X'.`,
            );
          if (!row.isf_record_group_name)
            validationErrors.push(
              `Row ${rowIndex}: 'isf_record_group_name' is required when is_isf is 'X'.`,
            );
        }

        // --- 4. Map to Entity (if validation passes for the row) ---
        if (validationErrors.length === initialErrorCount) {
          const categoryEntity: Partial<ArtifactCategories> = {
            tmfZoneNumber: this.formatNumForCsv(row.tmf_zone_num),
            tmfZoneName: row.tmf_zone_name || null,
            tmfSectionNumber: this.formatNumForCsv(row.tmf_section_num),
            tmfSectionName: row.tmf_section_name || null,
            tmfRecordGroupNumber: this.formatNumForCsv(
              row.tmf_record_group_num,
            ),
            tmfRecordGroupName: row.tmf_record_group_name || null,
            recordType: row.record_type,
            alternativeNames: row.alternative_names || null,
            isTMF: isTMF,
            isISF: isISF,
            isfZoneNumber: this.formatNumForCsv(row.isf_zone_num),
            isfZoneName: row.isf_zone_name || null,
            isfSectionNumber: this.formatNumForCsv(row.isf_section_num),
            isfSectionName: row.isf_section_name || null,
            isfRecordGroupNumber: this.formatNumForCsv(
              row.isf_record_group_num,
            ),
            isfRecordGroupName: row.isf_record_group_name || null,
            description: row.description || null,
            requiresSignature: this.parseBooleanFromCsv(
              row.requires_signature,
              'YES',
            ),
            expires: this.parseBooleanFromCsv(row.expires, 'YES'),
            inspectableRecord: this.parseBooleanFromCsv(
              row.inspectable_record,
              'YES',
            ),
            includesPHI: this.parseBooleanFromCsv(row.includes_phi, 'YES'),
            tmfCore: this.parseCoreFromCsv(row.core_or_recommended),
            origin: this.parseOriginFromCsv(row.origin),
            iitStudyArtifacts: this.parseIITStudyArtifactFromCsv(
              row.iit_study_artifact,
            ),
            tmfRefModelId: tmfMap.get(row.tmf_ref_model),
            isfRefModelId: isfMap.get(row.isf_ref_model),
            categoryVersionId: targetVersion.id,
          };
          categoriesToCreate.push(categoryEntity);
        }
      }

      if (validationErrors.length > 0) {
        // Log all errors for server-side debugging
        this.logger.warn({
          message: 'CSV import validation failed.',
          errorCount: validationErrors.length,
          errors: validationErrors,
        });

        // Construct a user-friendly message that includes the first specific error.
        const errorMessage = `CSV validation failed: ${validationErrors[0]}`;

        // Throw the exception with the combined message as a simple string.
        throw new BadRequestException(errorMessage);
      }
      if (categoriesToCreate.length === 0) {
        throw new BadRequestException(
          'CSV file is valid but contains no data to import.',
        );
      }

      // Check for duplicates before creating categories
      const uniqueList: Partial<ArtifactCategories>[] = [];
      const duplicateList: Partial<ArtifactCategories>[] = [];
      // Build duplicate check queries for each category to create
      if (existingVersion) {
        const conditions = [];
        categoriesToCreate.forEach((item) => {
          conditions.push({
            recordType: item.recordType,
            tmfZoneNumber: item.tmfZoneNumber,
            tmfSectionNumber: item.tmfSectionNumber,
            tmfRecordGroupNumber: item.tmfRecordGroupNumber,
            isfZoneNumber: item.isfZoneNumber,
            isfSectionNumber: item.isfSectionNumber,
            isfRecordGroupNumber: item.isfRecordGroupNumber,
            isTMF: item.isTMF,
            isISF: item.isISF,
          });
        });

        const existedCategories = await this.categoriesRepository
          .createQueryBuilder()
          .where({
            categoryVersionId: existingVersion.id,
          })
          .andWhere(conditions)
          .getMany();

        for (const item of categoriesToCreate) {
          if (
            existedCategories.some(
              (x) =>
                x.recordType === item.recordType &&
                x.tmfZoneNumber === item.tmfZoneNumber &&
                x.tmfSectionNumber === item.tmfSectionNumber &&
                x.tmfRecordGroupNumber === item.tmfRecordGroupNumber &&
                x.isfZoneNumber === item.isfZoneNumber &&
                x.isfSectionNumber === item.isfSectionNumber &&
                x.isfRecordGroupNumber === item.isfRecordGroupNumber &&
                x.isTMF === item.isTMF &&
                x.isISF === item.isISF,
            )
          ) {
            duplicateList.push(item);
            continue;
          }

          uniqueList.push(item);
        }

        if (duplicateList.length > 0) {
          this.logger.warn({
            message: 'Duplicate categories found during import',
            duplicatesCount: duplicateList.length,
            duplicates: duplicateList.map((d) => ({
              recordType: d.recordType,
              tmfZone: d.tmfZoneNumber,
              tmfSection: d.tmfSectionNumber,
              tmfRecordGroup: d.tmfRecordGroupNumber,
              isfZone: d.isfZoneNumber,
              isfSection: d.isfSectionNumber,
              isfRecordGroup: d.isfRecordGroupNumber,
            })),
          });
        }
      }

      // Only create unique categories
      const artifactCategoriesRepo =
        queryRunner.manager.getRepository(ArtifactCategories);

      if (existingVersion && uniqueList.length > 0) {
        await artifactCategoriesRepo.save(uniqueList, { chunk: 200 }); // Chunking for large imports
      } else if (!existingVersion) {
        await artifactCategoriesRepo.save(categoriesToCreate, { chunk: 200 }); // Chunking for large imports
      }

      await queryRunner.commitTransaction();

      return {
        message: importMessage,
        newVersionNumber: targetVersion.version,
        rowsProcessed: rows.length,
        categoriesCreated: existingVersion
          ? uniqueList.length
          : categoriesToCreate.length,
      };
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(
        { error },
        'Failed to import artifact categories. Transaction rolled back.',
      );
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async exportCategoriesAsCsv(version?: number): Promise<ExportResult> {
    const logMessage = version
      ? `Finding artifact categories for specific version: ${version}`
      : 'Finding latest artifact category version for export.';
    this.logger.debug(logMessage);

    let targetVersion: ArtifactCategoryVersions;

    if (version) {
      targetVersion = await this.categoryVersionsRepository.findOne({
        where: { version: version },
      });
      if (!targetVersion) {
        throw new NotFoundException(
          `Artifact category version ${version} does not exist.`,
        );
      }
    } else {
      // Case 2: No version provided, find the latest
      targetVersion = await this.categoryVersionsRepository.findOne({
        where: {},
        order: { version: 'DESC' },
      });
      if (!targetVersion) {
        throw new NotFoundException(
          'No artifact category versions found in the system.',
        );
      }
    }

    this.logger.info(
      `Exporting categories for version ${targetVersion.version} (ID: ${targetVersion.id})`,
    );

    // 2. Fetch all categories for the target version
    const categories = await this.categoriesRepository.find({
      where: { categoryVersion: { id: targetVersion.id } },
      relations: {
        tmfRefModel: true,
        isfRefModel: true,
        categoryVersion: true,
      },
      order: {
        tmfZoneNumber: 'ASC',
        isfZoneNumber: 'ASC',
        tmfSectionNumber: 'ASC',
        isfSectionNumber: 'ASC',
        tmfRecordGroupNumber: 'ASC',
        isfRecordGroupNumber: 'ASC',
        recordType: 'ASC',
      },
    });

    if (categories.length === 0) {
      this.logger.warn(
        `Version ${targetVersion.version} exists but has no associated categories.`,
      );
    }

    const dataForCsv = categories.map((category) => ({
      // --- Column names now match the example CSV file ---
      tmf_zone_num: this.formatNumForCsv(category.tmfZoneNumber),
      tmf_zone_name: category.tmfZoneName ?? '',
      tmf_section_num: this.formatNumForCsv(category.tmfSectionNumber),
      tmf_section_name: category.tmfSectionName ?? '',
      tmf_record_group_num: this.formatNumForCsv(category.tmfRecordGroupNumber),
      tmf_record_group_name: category.tmfRecordGroupName ?? '',
      record_type: category.recordType ?? '',
      alternative_names: category.alternativeNames ?? '', // Placeholder as per CSV, no direct mapping in schema
      is_tmf: this.formatBooleanX(category.isTMF),
      is_isf: this.formatBooleanX(category.isISF),
      isf_zone_num: this.formatNumForCsv(category.isfZoneNumber),
      isf_zone_name: category.isfZoneName ?? '',
      isf_section_num: this.formatNumForCsv(category.isfSectionNumber),
      isf_section_name: category.isfSectionName ?? '',
      isf_record_group_num: this.formatNumForCsv(category.isfRecordGroupNumber),
      isf_record_group_name: category.isfRecordGroupName ?? '',
      description: category.description ?? '',
      requires_signature: this.formatBooleanYesNo(category.requiresSignature),
      expires: this.formatBooleanYesNo(category.expires),
      inspectable_record: this.formatBooleanYesNo(category.inspectableRecord),
      core_or_recommended: this.formatCoreForCsv(category.tmfCore),
      includes_phi: this.formatBooleanYesNo(category.includesPHI),
      'Naming Convention': '', // Placeholder as per CSV
      origin: this.formatOriginForCsv(category.origin),
      iit_study_artifact: this.formatIITStudyArtifactForCsv(
        category.iitStudyArtifacts,
      ),
      tmf_ref_model: category.tmfRefModel?.tmfRefModel ?? '', // Safely access relational data
      isf_ref_model: category.isfRefModel?.isfRefModel ?? '', // Safely access relational data
      category_version: category.categoryVersion?.version ?? '',
    }));

    const csvContent = Papa.unparse(dataForCsv, {
      header: true,
      quotes: true, // Ensure fields with commas are properly quoted
    });

    this.logger.info(
      `Successfully generated CSV for ${categories.length} artifact categories.`,
    );

    return {
      csvData: csvContent,
      versionNumber: targetVersion.version,
    };
  }
  private parseCoreFromCsv(value: string | null): TMFCore | null {
    if (!value) return null;
    if (value.toLowerCase() === 'recommended') return TMFCore.RECOMMENDED;
    if (value.toLowerCase() === 'core') return TMFCore.CORE;
    return null;
  }

  private parseOriginFromCsv(value: string | null): OriginType | null {
    if (!value) return null;
    if (value.toUpperCase() === 'ISF') return OriginType.TO_ISF;
    if (value.toUpperCase() === 'TMF') return OriginType.TO_TMF;
    return null;
  }

  private parseIITStudyArtifactFromCsv(
    value: string | null,
  ): InvestigatorInitiatedStudyArtifacts {
    switch (value) {
      case 'D':
        return InvestigatorInitiatedStudyArtifacts.DEPENDENT;
      case 'M':
        return InvestigatorInitiatedStudyArtifacts.MANDATORY;
      case 'R':
        return InvestigatorInitiatedStudyArtifacts.RECOMMENDED;
      default:
        return InvestigatorInitiatedStudyArtifacts.RECOMMENDED; // Default to 'recommended' as per ticket
    }
  }
  private parseBooleanFromCsv(
    value: string,
    trueValue: 'X' | 'YES',
  ): boolean | null {
    const upperValue = value?.toUpperCase();
    if (upperValue === trueValue) return true;
    if (upperValue === 'NO') return false;
    return null; // Return null for empty or other values
  }
  private formatOriginForCsv(origin: OriginType | null): string {
    switch (origin) {
      case OriginType.TO_ISF:
        return 'ISF';
      case OriginType.TO_TMF:
        return 'TMF';
      default:
        return '';
    }
  }
  private formatNumForCsv(value: string | null | undefined): string {
    if (!value || value.length === 0) return '';
    return value.startsWith('0') ? value : '0' + value;
  }
  private formatBooleanYesNo(value: boolean | null | undefined): string {
    if (value === true) return 'YES';
    if (value === false) return 'NO';
    return '';
  }
  private formatBooleanX(value: boolean): string {
    return value ? 'X' : 'NO';
  }
  private formatIITStudyArtifactForCsv(
    iitStudyArtifact: InvestigatorInitiatedStudyArtifacts | null,
  ): string {
    switch (iitStudyArtifact) {
      case InvestigatorInitiatedStudyArtifacts.DEPENDENT:
        return 'D';
      case InvestigatorInitiatedStudyArtifacts.MANDATORY:
        return 'M';
      case InvestigatorInitiatedStudyArtifacts.RECOMMENDED:
        return 'R';
      default:
        return '';
    }
  }

  private formatCoreForCsv(core: TMFCore | null): string {
    switch (core) {
      case TMFCore.RECOMMENDED:
        return 'Recommended';
      case TMFCore.CORE:
        return 'Core';
      default:
        return '';
    }
  }
}
