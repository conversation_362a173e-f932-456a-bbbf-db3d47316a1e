/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-inferrable-types */
import { Type } from 'class-transformer';
import { IsOptional, IsInt, Min, IsIn, ValidateNested } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';

export class PaginationDto<T> {
  @ApiPropertyOptional({
    type: Object,
    description: 'Filter criteria for the query',
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => Object)
  filter?: T;

  @ApiPropertyOptional({
    type: Number,
    default: 1,
    minimum: 1,
    description: 'Page number',
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page: number = 1;

  @ApiPropertyOptional({
    type: Number,
    default: 100,
    minimum: 1,
    description: 'Number of items per page',
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  take: number = 100;

  @ApiPropertyOptional({
    enum: ['ASC', 'DESC', 'asc', 'desc'],
    description: 'Sort direction',
  })
  @IsOptional()
  @IsIn(['ASC', 'DESC', 'asc', 'desc'], {
    message: 'orderBy must be either ASC, DESC, asc, or desc',
  })
  orderDirection?: 'ASC' | 'DESC' | 'asc' | 'desc';

  @ApiPropertyOptional({
    type: String,
    description: 'Field name to sort by',
  })
  @IsOptional()
  orderBy?: string;

  get skip() {
    return (this.page - 1) * this.take;
  }

  get limit() {
    return +this.take;
  }
}

export class SimplePaginationRequestDto {
  @ApiPropertyOptional({
    type: Number,
    default: 0,
    minimum: 0,
    description: 'Page number',
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(0)
  page: number = 0;

  @ApiPropertyOptional({
    type: Number,
    default: 0,
    minimum: 0,
    description: 'Number of items per page',
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(0)
  take: number = 0;

  @ApiPropertyOptional({
    enum: ['ASC', 'DESC', 'asc', 'desc'],
    description: 'Sort direction',
  })
  @IsOptional()
  @IsIn(['ASC', 'DESC', 'asc', 'desc'], {
    message: 'orderBy must be either ASC, DESC, asc, or desc',
  })
  orderDirection?: 'ASC' | 'DESC' | 'asc' | 'desc';

  @ApiPropertyOptional({
    type: String,
    description: 'Field name to sort by',
  })
  @IsOptional()
  orderBy?: string;

  get skip() {
    return (this.page - 1) * this.take;
  }

  get limit() {
    return +this.take;
  }

  get isGetAll() {
    return !(this.take && this.page);
  }
}
