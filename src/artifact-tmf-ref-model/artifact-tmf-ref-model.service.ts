import {
  BadRequestException,
  ConflictException,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ArtifactTmfRefModel } from 'src/models/entities/artifact-tmf-ref-model.entity';
import { DataSource, ILike, Repository } from 'typeorm';
import { InjectPinoLogger, PinoLogger } from 'nestjs-pino';
import {
  TmfRefModelCreateDto,
  TmfRefModelUpdateDto,
} from './dto/create-artifact-tmf-ref-model.dto';
import { PaginationDto } from 'src/models/dto/pagination.dto';
import { FilterTmfRefModelDto } from 'src/models/dto/filters.dto';
import { MetadataDto } from 'src/models/dto/meta-data.dto';
import { ArtifactCategories } from 'src/models/entities/artifact-category.entity';

@Injectable()
export class ArtifactTmfRefModelService {
  constructor(
    @InjectRepository(ArtifactTmfRefModel)
    private refModelRepository: Repository<ArtifactTmfRefModel>,
    @InjectRepository(ArtifactCategories)
    private categoryRepo: Repository<ArtifactCategories>,
    @InjectPinoLogger(ArtifactTmfRefModelService.name)
    protected readonly logger: PinoLogger,
    protected dataSource: DataSource,
  ) {}
  async createTmfRefModel(
    refModelCreateDto: TmfRefModelCreateDto,
  ): Promise<ArtifactTmfRefModel> {
    try {
      const refModel = await this.refModelRepository.findOne({
        where: { tmfRefModel: refModelCreateDto.tmfRefModel },
      });
      if (refModel) {
        throw new BadRequestException('Ref Model already existed.');
      }
      const newRefModel = this.refModelRepository.create(refModelCreateDto);
      return this.refModelRepository.save(newRefModel);
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async getTmfRefModel(
    paginationDto: PaginationDto<FilterTmfRefModelDto>,
  ): Promise<{ results: ArtifactTmfRefModel[]; metadata: MetadataDto }> {
    try {
      const defaultOrderBy = 'id';
      const orderBy = paginationDto.orderBy || defaultOrderBy;
      const orderDirection =
        paginationDto.orderDirection || ('ASC' as 'ASC' | 'DESC');

      const options = {
        take: paginationDto.take,
        skip: (paginationDto.page - 1) * paginationDto.take,
        where: {
          ...(paginationDto.filter?.tmfRefModel && {
            tmfRefModel: ILike(`%${paginationDto.filter.tmfRefModel}%`),
          }),
        },
        order: { [orderBy]: orderDirection },
      };

      const [results, count] = await this.refModelRepository.findAndCount(
        options,
      );

      const metadata: MetadataDto = {
        totalCount: count,
        itemCount: results.length,
        itemsPerPage: paginationDto.take,
        totalPages: Math.ceil(count / paginationDto.take),
        currentPage: paginationDto.page,
      };

      return { results, metadata };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async getTmfRefModelById(modelId: string): Promise<ArtifactTmfRefModel> {
    try {
      const refModel = await this.refModelRepository.findOne({
        where: { id: modelId },
      });
      if (!refModel) {
        throw new NotFoundException('Ref Model not found.');
      }
      return refModel;
    } catch (error) {
      if (
        error instanceof BadRequestException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new InternalServerErrorException(
        'An unexpected error occurred while getting ref model by id.',
      );
    }
  }

  async updateTmfRefModel(
    modelId: string,
    refModelUpdateDto: TmfRefModelUpdateDto,
  ): Promise<ArtifactTmfRefModel> {
    try {
      const refModel = await this.refModelRepository.findOne({
        where: { id: modelId },
      });

      if (!refModel) {
        throw new NotFoundException('Ref Model not found');
      }

      const existingRefModel = await this.refModelRepository.findOne({
        where: { tmfRefModel: refModelUpdateDto.tmfRefModel },
      });
      if (existingRefModel && existingRefModel.id !== refModel.id) {
        throw new BadRequestException(
          `Ref Model with name ${refModelUpdateDto.tmfRefModel} already existed.`,
        );
      }

      refModel.tmfRefModel = refModelUpdateDto.tmfRefModel;
      if (refModelUpdateDto?.description) {
        refModel.description = refModelUpdateDto.description;
      }
      const refModelUpdated = await this.refModelRepository.save(refModel);
      return refModelUpdated;
    } catch (error) {
      throw error;
    }
  }

  async deleteRefModel(modelId: string) {
    try {
      const linkedCategoryCount = await this.categoryRepo.count({
        where: { tmfRefModelId: modelId },
      });

      if (linkedCategoryCount > 0) {
        throw new ConflictException(
          'Cannot delete models that link to an Artifact Category',
        );
      }

      const tmfRefModel = await this.getTmfRefModelById(modelId);
      return await this.refModelRepository.softRemove(tmfRefModel);
    } catch (error) {
      if (
        !(
          error instanceof NotFoundException ||
          error instanceof BadRequestException ||
          error instanceof ConflictException
        )
      ) {
        this.logger.error(
          `Failed to remove model ${modelId}: ${error.message}`,
          error.stack,
        );
      }
      throw error;
    }
  }
}
