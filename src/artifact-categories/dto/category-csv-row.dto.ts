export interface CategoryCsvRow {
  tmf_zone_num?: string;
  tmf_zone_name?: string;
  tmf_section_num?: string;
  tmf_section_name?: string;
  tmf_record_group_num?: string;
  tmf_record_group_name?: string;
  record_type?: string;
  alternative_names?: string;
  is_tmf?: string;
  is_isf?: string;
  isf_zone_num?: string;
  isf_zone_name?: string;
  isf_section_num?: string;
  isf_section_name?: string;
  isf_record_group_num?: string;
  isf_record_group_name?: string;
  description?: string;
  requires_signature?: string;
  expires?: string;
  inspectable_record?: string;
  core_or_recommended?: string;
  includes_phi?: string;
  origin?: string;
  iit_study_artifact?: string;
  tmf_ref_model?: string;
  isf_ref_model?: string;
  category_version?: string;
}
