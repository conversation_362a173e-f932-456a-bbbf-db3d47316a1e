// migration/sync-all.ts

import 'reflect-metadata';
import { DataSource } from 'typeorm';
import { AiPromptCache } from '../models/entities/ai-prompt-cache.entity';
import { AiPromptTemplate } from '../models/entities/ai-prompt-template.entity';
import { AiPromptVariable } from '../models/entities/ai-prompt-variable.entity';
import {
  ArtifactCategoryVersions,
  VersionStatus,
} from '../models/entities/artifact-category-version.entity';
import { ArtifactIsfRefModel } from '../models/entities/artifact-isf-ref-model.entity';
import { ArtifactTmfRefModel } from '../models/entities/artifact-tmf-ref-model.entity';
import { config as dotenvConfig } from 'dotenv';
import { ArtifactCategories } from '../models/entities/artifact-category.entity';
dotenvConfig({ path: '.env' });

async function syncAllEntities() {
  const ENTITIES = [
    AiPromptCache,
    AiPromptTemplate,
    AiPromptVariable,
    ArtifactCategoryVersions,
    ArtifactIsfRefModel,
    ArtifactTmfRefModel,
    ArtifactCategories,
  ];

  // Source DB
  const source = new DataSource({
    type: 'postgres',
    host: `${process.env.MAIN_APP_DATABASE_HOST}`,
    port: parseInt(process.env.MAIN_APP_DATABASE_PORT),
    username: `${process.env.MAIN_APP_DATABASE_USERNAME}`,
    password: `${process.env.MAIN_APP_DATABASE_PASSWORD}`,
    database: `${process.env.MAIN_APP_DATABASE_NAME}`,
    entities: ENTITIES,
  });

  // Destination DB
  const target = new DataSource({
    type: 'postgres',
    host: `${process.env.DATABASE_HOST}`,
    port: parseInt(process.env.DATABASE_PORT),
    username: `${process.env.DATABASE_USERNAME}`,
    password: `${process.env.DATABASE_PASSWORD}`,
    database: `${process.env.DATABASE_NAME}`,
    entities: ENTITIES,
  });

  await source.initialize();
  await target.initialize();

  for (const Entity of ENTITIES) {
    const sourceRepo = await source.getRepository(Entity);

    const batchSize = 500;
    let offset = 0;
    let count = 0;

    // Need remove different fields between two DB
    const isArtifactCategoryVersion =
      sourceRepo.metadata.tableName === 'artifact_category_versions';
    const props = sourceRepo.metadata.columns
      .filter(
        (col) =>
          col.databaseName !== 'deletedDate' &&
          (isArtifactCategoryVersion ? col.databaseName !== 'status' : true),
      )
      .map((col) => `"${col.databaseName}"`);

    console.log(
      `Starting sync data from main application for entity: ${Entity.name}`,
    );
    while (true) {
      const batch = await source.query(
        `SELECT ${props.join(', ')} FROM ${
          sourceRepo.metadata.tableName
        } OFFSET $1 LIMIT $2`,
        [offset, batchSize],
      );

      if (batch.length === 0) break;
      await target.getRepository(Entity).save(batch);

      offset += batchSize;
      count += batch.length;
    }
    console.log(
      `Completed sync data from main application for entity: ${Entity.name} with ${count} records`,
    );
  }

  await source.destroy();
  await target.destroy();
}

syncAllEntities().catch(console.error);
