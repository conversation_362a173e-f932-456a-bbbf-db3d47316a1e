import { registerAs } from '@nestjs/config';
import { config as dotenvConfig } from 'dotenv';
import { DataSource, DataSourceOptions } from 'typeorm';

dotenvConfig({ path: '.env' });

export const config = {
  type: 'postgres',
  host: `${process.env.DATABASE_HOST}`,
  port: parseInt(process.env.DATABASE_PORT),
  username: `${process.env.DATABASE_USERNAME}`,
  password: `${process.env.DATABASE_PASSWORD}`,
  database: `${process.env.DATABASE_NAME}`,
  entities: [__dirname + '/src/**/*.entity{.ts,.js}'],
  migrations: [__dirname + '/src/migrations/*{.ts,.js}'],
  // logging: false, // This will log all SQL queries to the console when true.
  logging: process.env.DATABASE_LOG === 'true' ? true : ['error', 'warn'],
  maxQueryExecutionTime: 1000, // Just logs queries taking > 1000ms
  autoLoadEntities: true,
  synchronize: false,
  extra: {
    max: 50, // maximum number of connections in pool
    min: 5, // minimum number of connections in pool
    idleTimeoutMillis: 30000, // how long a connection can be idle before being removed
    connectionTimeoutMillis: 2000, // how long to wait for a connection
    statement_timeout: 20000, // Add statement timeout
    // query_timeout: 10000,             // Add query timeout
    application_name: 'api', // Help identify connections
  },
  // Auto-reconnect settings
  retryAttempts: 3,
  retryDelay: 3000,
  keepConnectionAlive: true,
};

export default registerAs('typeorm', () => config);
export const connectionSource = new DataSource(config as DataSourceOptions);
