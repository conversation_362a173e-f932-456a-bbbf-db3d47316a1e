import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ConfigModule, ConfigService } from '@nestjs/config';
import typeorm from '../ormconfig';
import { TypeOrmModule } from '@nestjs/typeorm';
import { LoggerModule } from 'nestjs-pino';
import { AuthModule } from './auth/auth.module';
import { AiPromptTemplateModule } from './ai-prompt-template/ai-prompt-template.module';
import { M2MSyncModule } from './m2m-sync/m2m-sync.module';
import { AiPromptVariableModule } from './ai-prompt-variable/ai-prompt-variable.module';
import { ArtifactTmfRefModelModule } from './artifact-tmf-ref-model/artifact-tmf-ref-model.module';
import { ArtifactIsfRefModelModule } from './artifact-isf-ref-model/artifact-isf-ref-model.module';
import { ArtifactCategoriesModule } from './artifact-categories/artifact-categories.module';

@Module({
  imports: [
    M2MSyncModule,
    ConfigModule.forRoot({
      isGlobal: true,
      load: [typeorm],
    }),
    TypeOrmModule.forRootAsync({
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) =>
        configService.get('typeorm'),
    }),
    LoggerModule.forRoot({
      pinoHttp: {
        transport:
          process.env.APP_ENV === 'local'
            ? {
                target: 'pino-pretty',
                options: {
                  singleLine: true,
                  colorize: true,
                  levelFirst: true,
                  translateTime: 'UTC:yyyy-mm-dd HH:MM:ss',
                },
              }
            : undefined,
        // Optional: customize log levels
        customLevels: {
          trace: 10,
          debug: 20,
          info: 30,
          warn: 40,
          error: 50,
          fatal: 60,
        },
      },
    }),
    AuthModule,
    AiPromptTemplateModule,
    AiPromptVariableModule,
    ArtifactTmfRefModelModule,
    ArtifactIsfRefModelModule,
    ArtifactCategoriesModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
