DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_USERNAME=postgres
DATABASE_PASSWORD=123456
DATABASE_NAME=clincove-config
DATABASE_LOG=true

MAIN_APP_DATABASE_HOST=localhost
MAIN_APP_DATABASE_PORT=5432
MAIN_APP_DATABASE_USERNAME=postgres
MAIN_APP_DATABASE_PASSWORD=123456
MAIN_APP_DATABASE_NAME=clincove

CORS_ORIGINS=https://dev-api.us.clincove.com,http://localhost:3001,http://localhost:3000
INTERNAL_API_KEY=128_bit_api_key
ACCESS_TOKEN_EXPIRES_IN=1h

# Clerk
CLERK_SECRET_KEY=this_is_clerk_secret_key
