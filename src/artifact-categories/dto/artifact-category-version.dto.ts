import { ApiProperty, PartialType } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsDate,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';

export class CategoryVersionInfoDto {
  @ApiProperty({
    description: 'Effective date',
    required: true,
  })
  @IsNotEmpty()
  @IsDate()
  @Type(() => Date)
  effectiveDate: Date;

  @ApiProperty({
    description: 'Version number',
    required: true,
  })
  @IsNotEmpty()
  @IsNumber()
  version: number;

  @ApiProperty({
    description: 'Notes about this version',
    required: false,
  })
  @IsOptional()
  @IsString()
  notes?: string;
}

export class UpdateCategoryVersionDto extends PartialType(
  CategoryVersionInfoDto,
) {}
