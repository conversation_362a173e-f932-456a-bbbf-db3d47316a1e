import { Test, TestingModule } from '@nestjs/testing';
import { ArtifactIsfRefModelController } from './artifact-isf-ref-model.controller';
import { ArtifactIsfRefModelService } from './artifact-isf-ref-model.service';

describe('ArtifactIsfRefModelController', () => {
  let controller: ArtifactIsfRefModelController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ArtifactIsfRefModelController],
      providers: [ArtifactIsfRefModelService],
    }).compile();

    controller = module.get<ArtifactIsfRefModelController>(
      ArtifactIsfRefModelController,
    );
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
