import { Entity, Column, Index } from 'typeorm';
import { BaseEntity } from '../abstracts/abstract-entity';

export enum AiPromptTemplateKey {
  PROTOCOL_EXPLORER = 'protocolExplorer',
  ISF_ARTIFACT_CATEGORY = 'isfArtifactCategory',
  TMF_ARTIFACT_CATEGORY = 'tmfArtifactCategory',
}

export enum AiModelProvider {
  GEMINI = 'gemini',
  OPENAI = 'openai',
}

export type ChatRole = 'user' | 'model' | 'system';

export interface ChatMessagePart {
  text: string;
}

export interface GenerationConfig {
  temperature?: number;
  topP?: number;
  topK?: number;
}

export interface ChatMessage {
  role: ChatRole;
  parts: ChatMessagePart[];
  isCacheable?: boolean;
  cacheContentId?: string;
}
export enum PromptContent {
  PROTOCOL_CONTENT = '{{PROTOCOL_CONTENT}}',
  ARTIFACT_CATEGORIES_CONTENT = '{{ARTIFACT_CATEGORIES_CONTENT}}',
}

@Entity('ai_prompt_template', { synchronize: false })
@Index(['lastUpdatedDate'])
@Index(['deletedDate'])
export class AiPromptTemplate extends BaseEntity {
  @Column({ type: 'varchar', length: 255, nullable: false })
  name: string;

  @Column({ type: 'text', nullable: true })
  description?: string;

  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  @Column({ type: 'int', default: 1 })
  version: number;

  @Column({
    type: 'enum',
    enum: AiPromptTemplateKey,
    nullable: false,
  })
  key: AiPromptTemplateKey;

  @Column({
    type: 'jsonb',
    nullable: false,
    comment: 'Array of chat messages with role and EJS template parts',
  })
  templateContent: ChatMessage[];

  @Column({
    type: 'jsonb',
    nullable: true,
    default: () => `'{"topK": 40, "topP": 0.8, "temperature": 0}'::jsonb`,
  })
  generationConfig?: any;

  @Column({
    type: 'enum',
    enum: AiModelProvider,
    nullable: false,
  })
  modelProvider: AiModelProvider;

  @Column({ type: 'varchar', length: 100, nullable: false })
  model: string;
}
