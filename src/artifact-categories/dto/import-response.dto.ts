import { ApiProperty } from '@nestjs/swagger';

export class ImportCategoriesResultDto {
  @ApiProperty({
    description: 'A summary message of the import operation.',
    example: 'Import successful. New version created.',
  })
  message: string;

  @ApiProperty({
    description:
      'The version number of the newly created artifact category version.',
    example: 4,
  })
  newVersionNumber: number;

  @ApiProperty({
    description:
      'The total number of data rows found and processed in the CSV file.',
    example: 52,
  })
  rowsProcessed: number;

  @ApiProperty({
    description:
      'The number of new artifact categories successfully created and saved.',
    example: 52,
  })
  categoriesCreated: number;
}
